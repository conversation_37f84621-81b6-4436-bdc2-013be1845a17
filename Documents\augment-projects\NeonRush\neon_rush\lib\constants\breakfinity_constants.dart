import 'package:flutter/material.dart';

/// Constants and configuration for the Breakfinity game
class BreakfinityConstants {
  // Game Structure
  static const int totalLayers = 1000;
  static const int sectionsPerLayer = 49; // 7x7 grid per layer (increased from 5x5)
  static const int sectionsPerRow = 7; // Increased from 5 to make sections smaller and fill viewport better
  
  // Visual Settings
  static const double sectionSize = 45.0; // Reduced from 60.0 to make sections smaller
  static const double sectionSpacing = 2.0; // Reduced from 4.0 for tighter spacing
  static const double layerSpacing = 20.0;
  static const double viewportHeight = 400.0;
  
  // Gameplay
  static const int baseDamage = 1; // Default starting damage
  static const double baseTapMultiplier = 1.0;
  static const int maxAutoTappers = 10;
  static const Duration autoTapInterval = Duration(milliseconds: 500);
  
  // Progression
  static const int baseTokenReward = 5;
  static const double tokenMultiplierPerLayer = 0.1;
  static const int layerCompletionBonus = 100;
  
  // Power-ups
  static const Duration megaTapDuration = Duration(seconds: 30);
  static const Duration speedBoostDuration = Duration(seconds: 20);
  static const Duration revealAllDuration = Duration(seconds: 10);
  
  // Visual Effects
  static const Duration tapAnimationDuration = Duration(milliseconds: 200);
  static const Duration crackAnimationDuration = Duration(milliseconds: 300);
  static const Duration destroyAnimationDuration = Duration(milliseconds: 500);
  static const Duration layerTransitionDuration = Duration(milliseconds: 800);
  
  // Colors
  static const Color backgroundColor = Color(0xFF0A0A0A);
  static const Color structureColor = Color(0xFF1A1A2E);
  static const Color glowColor = Color(0xFF00FFFF);
  static const Color crackColor = Color(0xFFFF6B6B);
  static const Color destroyColor = Color(0xFFFFD93D);
  
  // Section Type Colors
  static const Map<String, Color> sectionTypeColors = {
    'normal': Color(0xFF4ECDC4),
    'weak': Color(0xFF95E1D3),
    'reinforced': Color(0xFF3D5A80),
    'crystal': Color(0xFFFFD93D),
    'mystery': Color(0xFF9B59B6),
    'explosive': Color(0xFFE74C3C),
    'regenerating': Color(0xFF2ECC71),
  };
  
  // Audio
  static const String tapSoundPath = 'assets/sounds/tap.wav';
  static const String crackSoundPath = 'assets/sounds/crack.wav';
  static const String destroySoundPath = 'assets/sounds/destroy.wav';
  static const String layerCompleteSoundPath = 'assets/sounds/layer_complete.wav';
  
  // Particle Effects
  static const int tapParticleCount = 5;
  static const int destroyParticleCount = 15;
  static const int layerCompleteParticleCount = 50;
  static const Duration particleLifetime = Duration(milliseconds: 1000);
  
  // Auto-save
  static const Duration autoSaveInterval = Duration(seconds: 30);
  static const Duration progressUpdateInterval = Duration(milliseconds: 100);
  
  // Performance
  static const int maxVisibleLayers = 3;
  static const int maxParticles = 100;
  static const double cullingDistance = 1000.0;
  
  // Rewards
  static const Map<int, Map<String, dynamic>> milestoneRewards = {
    10: {'tokens': 500, 'powerUp': 'mega_tap'},
    25: {'tokens': 1000, 'upgrade': 'damage_boost'},
    50: {'tokens': 2500, 'theme': 'neon_blue'},
    100: {'tokens': 5000, 'powerUp': 'auto_tapper'},
    250: {'tokens': 10000, 'theme': 'cyber_purple'},
    500: {'tokens': 25000, 'upgrade': 'layer_scanner'},
    1000: {'tokens': 100000, 'theme': 'rainbow_infinity'},
  };
  
  // Special Events
  static const double crystalSpawnBaseChance = 0.05; // 5% base chance
  static const double mysterySpawnBaseChance = 0.03; // 3% base chance
  static const double explosiveSpawnBaseChance = 0.02; // 2% base chance
  
  // Idle Mechanics
  static const Duration offlineTimeLimit = Duration(hours: 24);
  static const double offlineProgressMultiplier = 0.1; // 10% of online progress
  static const int maxOfflineReward = 10000;
  
  // UI Layout
  static const double headerHeight = 80.0;
  static const double bottomPanelHeight = 120.0;
  static const double sidebarWidth = 200.0;
  static const EdgeInsets screenPadding = EdgeInsets.all(16.0);
  
  // Animation Curves
  static const Curve tapAnimationCurve = Curves.elasticOut;
  static const Curve destroyAnimationCurve = Curves.easeInOut;
  static const Curve layerTransitionCurve = Curves.easeInOutCubic;
  
  // Haptic Feedback
  static const bool enableHapticFeedback = true;
  static const Duration hapticDelay = Duration(milliseconds: 50);
  
  // Debug
  static const bool showDebugInfo = false;
  static const bool showPerformanceOverlay = false;
  static const bool enableLogging = true;
}

/// Power-up definitions for Breakfinity
class BreakfinityPowerUps {
  static const Map<String, Map<String, dynamic>> powerUps = {
    'mega_tap': {
      'name': 'Mega Tap',
      'description': 'Increases tap damage by 5x for 30 seconds',
      'icon': Icons.flash_on,
      'duration': 30000, // milliseconds
      'cost': 100,
      'color': 0xFFFFD93D,
      'effect': {'damage_multiplier': 5.0},
    },

    'layer_bomb': {
      'name': 'Layer Bomb',
      'description': 'Destroys 25% of remaining sections in current layer',
      'icon': Icons.flash_on,
      'duration': 0, // Instant effect
      'cost': 500,
      'color': 0xFFE74C3C,
      'effect': {'destroy_percentage': 0.25},
    },
    'auto_tapper': {
      'name': 'Auto-Tapper',
      'description': 'Automatically taps sections for 30 seconds',
      'icon': Icons.touch_app,
      'duration': 30000, // 30 seconds
      'cost': 150,
      'color': 0xFF9B59B6,
      'effect': {'auto_tapper_active': true},
    },
  };

  /// Get power-up configuration by ID
  static Map<String, dynamic>? getPowerUp(String id) {
    return powerUps[id];
  }

  /// Get all available power-ups
  static List<String> getAllPowerUpIds() {
    return powerUps.keys.toList();
  }

  /// Calculate power-up cost with scaling
  static int getPowerUpCost(String id, int timesUsed) {
    final powerUp = powerUps[id];
    if (powerUp == null) return 0;
    
    final baseCost = powerUp['cost'] as int;
    // Cost increases by 10% each time used
    return (baseCost * (1.0 + (timesUsed * 0.1))).round();
  }
}

/// Theme definitions for Breakfinity
class BreakfinityThemes {
  static const Map<String, Map<String, dynamic>> themes = {
    'default': {
      'name': 'Neon Default',
      'background': 0xFF0A0A0A,
      'structure': 0xFF1A1A2E,
      'glow': 0xFF00FFFF,
      'accent': 0xFFFFD93D,
      'particles': [0xFF00FFFF, 0xFFFFD93D, 0xFFFF6B6B],
    },
    'cyber_blue': {
      'name': 'Cyber Blue',
      'background': 0xFF0D1B2A,
      'structure': 0xFF1B263B,
      'glow': 0xFF00D4FF,
      'accent': 0xFF80E5FF,
      'particles': [0xFF00D4FF, 0xFF80E5FF, 0xFF00A8CC],
    },
    'neon_pink': {
      'name': 'Neon Pink',
      'background': 0xFF1A0A1A,
      'structure': 0xFF2E1A2E,
      'glow': 0xFFFF00FF,
      'accent': 0xFFFF80FF,
      'particles': [0xFFFF00FF, 0xFFFF80FF, 0xFFCC00CC],
    },
    'cyber_purple': {
      'name': 'Cyber Purple',
      'background': 0xFF1A0A2E,
      'structure': 0xFF2E1A3E,
      'glow': 0xFF8000FF,
      'accent': 0xFFB380FF,
      'particles': [0xFF8000FF, 0xFFB380FF, 0xFF6600CC],
    },
    'electric_green': {
      'name': 'Electric Green',
      'background': 0xFF0A1A0A,
      'structure': 0xFF1A2E1A,
      'glow': 0xFF00FF00,
      'accent': 0xFF80FF80,
      'particles': [0xFF00FF00, 0xFF80FF80, 0xFF00CC00],
    },
    'fire_orange': {
      'name': 'Fire Orange',
      'background': 0xFF1A1A0A,
      'structure': 0xFF2E2E1A,
      'glow': 0xFFFF8000,
      'accent': 0xFFFFB380,
      'particles': [0xFFFF8000, 0xFFFFB380, 0xFFCC6600],
    },
    'rainbow_infinity': {
      'name': 'Rainbow Infinity',
      'background': 0xFF0A0A0A,
      'structure': 0xFF1A1A1A,
      'glow': 0xFFFFFFFF, // Changes dynamically
      'accent': 0xFFFFFFFF, // Changes dynamically
      'particles': [0xFFFF0000, 0xFF00FF00, 0xFF0000FF, 0xFFFFFF00, 0xFFFF00FF, 0xFF00FFFF],
      'animated': true,
    },
  };

  /// Get theme configuration by ID
  static Map<String, dynamic>? getTheme(String id) {
    return themes[id];
  }

  /// Get all available theme IDs
  static List<String> getAllThemeIds() {
    return themes.keys.toList();
  }

  /// Check if theme is animated
  static bool isAnimatedTheme(String id) {
    final theme = themes[id];
    return theme?['animated'] == true;
  }
}
