import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../core/sound_manager.dart';
import '../models/neon_theme.dart';
import '../models/challenge.dart';
import '../services/challenges_service.dart';
import '../ui/neon_text.dart';
import '../ui/neon_button.dart';
import '../ui/neon_container.dart';


/// Neon Snake bonus game
class NeonSnakeGame extends StatefulWidget {
  const NeonSnakeGame({super.key});

  @override
  State<NeonSnakeGame> createState() => _NeonSnakeGameState();
}

class _NeonSnakeGameState extends State<NeonSnakeGame>
    with TickerProviderStateMixin {
  static const int gridSize = 20;
  static const double cellSize = 15.0;
  
  late Timer _gameTimer;
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;
  
  List<Point<int>> snake = [Point(10, 10)];
  Point<int> food = Point(5, 5);
  Point<int> direction = Point(1, 0);
  Point<int> nextDirection = Point(1, 0);
  
  int score = 0;
  bool isGameRunning = false;
  bool isGameOver = false;
  bool isPaused = false;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _generateFood();
    _startGame();
  }

  void _setupAnimations() {
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _glowController.dispose();
    super.dispose();
  }

  void _startGame() {
    isGameRunning = true;
    isGameOver = false;
    isPaused = false;
    _gameTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!isPaused) {
        _updateGame();
      }
    });
  }

  void _updateGame() {
    if (!isGameRunning || isGameOver) return;

    setState(() {
      direction = nextDirection;
      
      // Move snake head
      Point<int> newHead = Point(
        snake.first.x + direction.x,
        snake.first.y + direction.y,
      );

      // Check wall collision
      if (newHead.x < 0 || newHead.x >= gridSize ||
          newHead.y < 0 || newHead.y >= gridSize) {
        _gameOver();
        return;
      }

      // Check self collision
      if (snake.contains(newHead)) {
        _gameOver();
        return;
      }

      snake.insert(0, newHead);

      // Check food collision
      if (newHead == food) {
        score += 10;
        _generateFood();
        SoundManager().playSfx(SoundType.buttonClick); // Use available sound
      } else {
        snake.removeLast();
      }
    });
  }

  void _generateFood() {
    Random random = Random();
    Point<int> newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood));
    food = newFood;
  }

  void _gameOver() {
    isGameOver = true;
    isGameRunning = false;
    _gameTimer.cancel();

    // Update challenge progress for bonus game completion
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.bonusGames,
      type: ChallengeType.bonusGame,
      amount: 1,
      metadata: {
        'gameType': 'neonSnake',
        'score': score,
        'snakeLength': snake.length,
        'action': 'gameCompleted',
      },
    );
  }

  void _resetGame() {
    setState(() {
      snake = [Point(10, 10)];
      direction = Point(1, 0);
      nextDirection = Point(1, 0);
      score = 0;
      isGameOver = false;
      _generateFood();
    });
    _startGame();
  }

  void _pauseGame() {
    setState(() {
      isPaused = !isPaused;
    });
  }

  void _changeDirection(Point<int> newDirection) {
    // Prevent reversing into itself
    if (newDirection.x != -direction.x || newDirection.y != -direction.y) {
      nextDirection = newDirection;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Center(
                child: _buildGameArea(),
              ),
            ),
            _buildControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          NeonCircularButton(
            icon: Icons.arrow_back,
            glowColor: NeonThemes.cyberBlue.primary,
            size: 50,
            iconSize: 24,
            onPressed: () async {
              await SoundManager().playSfx(SoundType.buttonClick);
              if (mounted) Navigator.of(context).pop();
            },
          ),
          
          const SizedBox(width: 20),
          
          // Title
          Expanded(
            child: NeonText.title(
              'NEON SNAKE',
              glowColor: NeonThemes.electricGreen.primary,
              fontSize: 24,
              fontWeight: FontWeight.w900,
            ),
          ),
          
          // Score
          NeonContainer.card(
            glowColor: NeonThemes.electricGreen.primary,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: NeonText.body(
                'Score: $score',
                glowColor: NeonThemes.electricGreen.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 10),
          
          // Pause button
          NeonCircularButton(
            icon: isPaused ? Icons.play_arrow : Icons.pause,
            glowColor: NeonThemes.neonPink.primary,
            size: 50,
            iconSize: 24,
            onPressed: _pauseGame,
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return NeonContainer.card(
          glowColor: NeonThemes.cyberBlue.primary,
          glowIntensity: _glowAnimation.value * 0.8,
          child: Container(
            width: gridSize * cellSize,
            height: gridSize * cellSize,
            color: Colors.black.withValues(alpha: 0.8),
            child: Stack(
              children: [
                // Game grid
                CustomPaint(
                  size: Size(gridSize * cellSize, gridSize * cellSize),
                  painter: SnakeGamePainter(
                    snake: snake,
                    food: food,
                    cellSize: cellSize,
                    glowIntensity: _glowAnimation.value,
                  ),
                ),
                
                // Game over overlay
                if (isGameOver) _buildGameOverOverlay(),
                
                // Pause overlay
                if (isPaused && !isGameOver) _buildPauseOverlay(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameOverOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            NeonText.title(
              'GAME OVER',
              glowColor: Colors.red,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 16),
            NeonText.body(
              'Final Score: $score',
              glowColor: NeonThemes.electricGreen.primary,
              fontSize: 18,
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: 120,
              height: 40,
              child: NeonButton.primary(
                text: 'RESTART',
                glowColor: NeonThemes.electricGreen.primary,
                onPressed: _resetGame,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withValues(alpha: 0.6),
      child: Center(
        child: NeonText.title(
          'PAUSED',
          glowColor: NeonThemes.neonPink.primary,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Directional controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  // Up
                  NeonCircularButton(
                    icon: Icons.keyboard_arrow_up,
                    glowColor: NeonThemes.cyberBlue.primary,
                    size: 60,
                    iconSize: 30,
                    onPressed: () => _changeDirection(Point(0, -1)),
                  ),
                  const SizedBox(height: 10),
                  // Down
                  NeonCircularButton(
                    icon: Icons.keyboard_arrow_down,
                    glowColor: NeonThemes.cyberBlue.primary,
                    size: 60,
                    iconSize: 30,
                    onPressed: () => _changeDirection(Point(0, 1)),
                  ),
                ],
              ),
              const SizedBox(width: 40),
              Column(
                children: [
                  // Left
                  NeonCircularButton(
                    icon: Icons.keyboard_arrow_left,
                    glowColor: NeonThemes.cyberBlue.primary,
                    size: 60,
                    iconSize: 30,
                    onPressed: () => _changeDirection(Point(-1, 0)),
                  ),
                  const SizedBox(height: 10),
                  // Right
                  NeonCircularButton(
                    icon: Icons.keyboard_arrow_right,
                    glowColor: NeonThemes.cyberBlue.primary,
                    size: 60,
                    iconSize: 30,
                    onPressed: () => _changeDirection(Point(1, 0)),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Custom painter for the snake game
class SnakeGamePainter extends CustomPainter {
  final List<Point<int>> snake;
  final Point<int> food;
  final double cellSize;
  final double glowIntensity;

  SnakeGamePainter({
    required this.snake,
    required this.food,
    required this.cellSize,
    required this.glowIntensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw grid lines
    _drawGrid(canvas, size);

    // Draw food
    _drawFood(canvas);

    // Draw snake
    _drawSnake(canvas);
  }

  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = NeonThemes.cyberBlue.primary.withValues(alpha: 0.1)
      ..strokeWidth = 0.5;

    // Vertical lines
    for (int i = 0; i <= size.width / cellSize; i++) {
      canvas.drawLine(
        Offset(i * cellSize, 0),
        Offset(i * cellSize, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (int i = 0; i <= size.height / cellSize; i++) {
      canvas.drawLine(
        Offset(0, i * cellSize),
        Offset(size.width, i * cellSize),
        paint,
      );
    }
  }

  void _drawFood(Canvas canvas) {
    final paint = Paint()
      ..color = NeonThemes.neonPink.primary
      ..style = PaintingStyle.fill;

    final glowPaint = Paint()
      ..color = NeonThemes.neonPink.primary.withValues(alpha: 0.3 * glowIntensity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    final center = Offset(
      food.x * cellSize + cellSize / 2,
      food.y * cellSize + cellSize / 2,
    );

    // Draw glow
    canvas.drawCircle(center, cellSize * 0.6, glowPaint);

    // Draw food
    canvas.drawCircle(center, cellSize * 0.3, paint);
  }

  void _drawSnake(Canvas canvas) {
    for (int i = 0; i < snake.length; i++) {
      final segment = snake[i];
      final isHead = i == 0;

      final color = isHead
          ? NeonThemes.electricGreen.primary
          : NeonThemes.electricGreen.secondary;

      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      final glowPaint = Paint()
        ..color = color.withValues(alpha: 0.4 * glowIntensity)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

      final rect = Rect.fromLTWH(
        segment.x * cellSize + 1,
        segment.y * cellSize + 1,
        cellSize - 2,
        cellSize - 2,
      );

      final glowRect = Rect.fromLTWH(
        segment.x * cellSize - 2,
        segment.y * cellSize - 2,
        cellSize + 4,
        cellSize + 4,
      );

      // Draw glow
      canvas.drawRRect(
        RRect.fromRectAndRadius(glowRect, const Radius.circular(4)),
        glowPaint,
      );

      // Draw segment
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(2)),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
