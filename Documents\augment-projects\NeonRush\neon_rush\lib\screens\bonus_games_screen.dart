import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants.dart';
import '../providers/game_provider.dart';
import '../widgets/token_balance.dart';
import '../services/spin_wheel_service.dart';
import '../models/token_transaction.dart';
import '../design/components/neon_button.dart';
import 'bonus_games/neon_snake_screen.dart';
import 'bonus_games/neon_pong_screen.dart';
import 'bonus_games/astro_blaster_screen.dart';
import 'bonus_games/brick_blast_screen.dart';

import 'bonus_games/glow_flow_screen.dart';
import 'bonus_games/neon_stack_screen.dart';
import 'bonus_games/swipe_slice_screen.dart';
import 'bonus_games/tap_runner_screen.dart';
import 'bonus_games/neon_dig_screen.dart';
import 'bonus_games/slide_match_screen.dart';
import 'bonus_games/tap_shot_screen.dart';
import 'bonus_games/drag_maze_screen.dart';
import 'bonus_games/color_zap_screen.dart';
import 'bonus_games/bounce_tap_screen.dart';
import 'bonus_games/tap_craft_screen.dart';

/// Screen for bonus games unlocked after completing all standard levels
class BonusGamesScreen extends StatefulWidget {
  const BonusGamesScreen({super.key});

  @override
  State<BonusGamesScreen> createState() => _BonusGamesScreenState();
}

class _BonusGamesScreenState extends State<BonusGamesScreen> {
  int _bonusGameKeys = 0;
  Set<String> _pinnedGames = <String>{};
  Map<String, int> _trialCounts = <String, int>{};

  final List<BonusGame> bonusGames = [
    BonusGame(
      id: 'neon_snake',
      name: 'Neon Snake',
      description: 'Classic snake with neon twist',
      icon: Icons.timeline,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'neon_pong',
      name: 'Neon Pong',
      description: 'Retro pong with modern effects',
      icon: Icons.sports_tennis,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'astro_blaster',
      name: 'Astro Blaster',
      description: 'Space shooter adventure',
      icon: Icons.rocket_launch,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'brick_blast',
      name: 'BrickBlast',
      description: 'Physics-driven neon brick-breaker',
      icon: Icons.sports_baseball,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),

    BonusGame(
      id: 'glow_flow',
      name: 'Glow Flow',
      description: 'Connect the flowing lights',
      icon: Icons.water_drop,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'neon_stack',
      name: 'Neon Stack',
      description: 'Stack blocks to reach the top',
      icon: Icons.layers,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'swipe_slice',
      name: 'Swipe Slice',
      description: 'Slice through neon objects',
      icon: Icons.content_cut,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'tap_runner',
      name: 'TapRunner',
      description: 'Run and jump through obstacles',
      icon: Icons.directions_run,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'neon_dig',
      name: 'Neon Dig',
      description: 'Dig deep for hidden treasures',
      icon: Icons.construction,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'slide_match',
      name: 'SlideMatch',
      description: 'Match sliding puzzle pieces',
      icon: Icons.grid_view,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'tap_shot',
      name: 'TapShot',
      description: 'Aim and shoot targets',
      icon: Icons.gps_fixed,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'drag_maze',
      name: 'Drag Maze',
      description: 'Navigate through neon mazes',
      icon: Icons.route,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'color_zap',
      name: 'ColorZap',
      description: 'Zap matching colors quickly',
      icon: Icons.palette,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'bounce_tap',
      name: 'BounceTap',
      description: 'Bounce and tap to score',
      icon: Icons.sports_basketball,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
    BonusGame(
      id: 'tap_craft',
      name: 'TapCraft',
      description: 'Craft items with precise taps',
      icon: Icons.build,
      keyCost: GameConstants.bonusGameKeysRequired,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadBonusGameKeys();
    _loadPinnedGames();
    _loadTrialCounts();
  }

  Future<void> _loadBonusGameKeys() async {
    final keys = await SpinWheelService.getBonusGameKeyCount();
    setState(() {
      _bonusGameKeys = keys;
    });
  }

  Future<void> _loadPinnedGames() async {
    final prefs = await SharedPreferences.getInstance();
    final pinnedList = prefs.getStringList('pinned_bonus_games') ?? [];
    setState(() {
      _pinnedGames = pinnedList.toSet();
    });
  }

  Future<void> _togglePin(String gameId) async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      if (_pinnedGames.contains(gameId)) {
        _pinnedGames.remove(gameId);
      } else {
        _pinnedGames.add(gameId);
      }
    });
    await prefs.setStringList('pinned_bonus_games', _pinnedGames.toList());
  }

  Future<void> _loadTrialCounts() async {
    final prefs = await SharedPreferences.getInstance();
    final Map<String, int> trialCounts = {};
    for (final game in bonusGames) {
      trialCounts[game.id] = prefs.getInt('trial_count_${game.id}') ?? 0;
    }
    setState(() {
      _trialCounts = trialCounts;
    });
  }

  Future<void> _useTrialPlay(String gameId) async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = _trialCounts[gameId] ?? 0;
    final newCount = currentCount + 1;

    setState(() {
      _trialCounts[gameId] = newCount;
    });

    await prefs.setInt('trial_count_$gameId', newCount);
  }

  int _getRemainingTrials(String gameId) {
    const maxTrials = 3;
    final usedTrials = _trialCounts[gameId] ?? 0;
    return (maxTrials - usedTrials).clamp(0, maxTrials);
  }

  void _playTrialGame(BonusGame game) async {
    // Use a trial play
    await _useTrialPlay(game.id);

    // Navigate to the game in trial mode
    if (mounted) {
      await _navigateToGame(game, isTrialMode: true);
    }
  }

  Future<void> _navigateToGame(BonusGame game, {bool isTrialMode = false}) async {
    // Show trial mode indicator if in trial
    if (isTrialMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Trial Mode: ${_getRemainingTrials(game.id)} trials remaining',
            style: GoogleFonts.orbitron(color: NeonColors.textPrimary),
          ),
          backgroundColor: NeonColors.primaryAccent.withValues(alpha: 0.8),
          duration: const Duration(seconds: 2),
        ),
      );
    }

    // Navigate to the appropriate game screen
    switch (game.id) {
      case 'neon_snake':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const NeonSnakeScreen(),
          ),
        );
        break;
      case 'neon_pong':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const NeonPongScreen(),
          ),
        );
        break;
      case 'astro_blaster':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AstroBlasterScreen(),
          ),
        );
        break;
      case 'brick_blast':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const BrickBlastScreen(),
          ),
        );
        break;

      case 'glow_flow':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const GlowFlowScreen(),
          ),
        );
        break;
      case 'neon_stack':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const NeonStackScreen(),
          ),
        );
        break;
      case 'swipe_slice':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SwipeSliceScreen(),
          ),
        );
        break;
      case 'tap_runner':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const TapRunnerScreen(),
          ),
        );
        break;
      case 'neon_dig':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const NeonDigScreen(),
          ),
        );
        break;
      case 'slide_match':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SlideMatchScreen(),
          ),
        );
        break;
      case 'tap_shot':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const TapShotScreen(),
          ),
        );
        break;
      case 'drag_maze':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const DragMazeScreen(),
          ),
        );
        break;
      case 'color_zap':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const ColorZapScreen(),
          ),
        );
        break;
      case 'bounce_tap':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const BounceTapScreen(),
          ),
        );
        break;
      case 'tap_craft':
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const TapCraftScreen(),
          ),
        );
        break;
    }
  }

  Future<void> _buyBonusGameKey(GameProvider gameProvider) async {
    final cost = GameConstants.bonusGameKeyUnlockCost;
    if (gameProvider.tokens < cost) return;

    try {
      // Spend tokens
      await gameProvider.spendTokens(cost, TokenTransactionType.bonusGameKey, 'Buy Bonus Game Key');

      // Award bonus game key
      await SpinWheelService.awardBonusGameKeys(1);

      // Refresh key count
      await _loadBonusGameKeys();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Bonus Game Key purchased!',
              style: GoogleFonts.orbitron(color: NeonColors.textPrimary),
            ),
            backgroundColor: NeonColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to purchase key: $e',
              style: GoogleFonts.orbitron(color: NeonColors.textPrimary),
            ),
            backgroundColor: NeonColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Bonus Games',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          const TokenBalance(),
          const SizedBox(width: 16),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonColors.backgroundGradient,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: NeonColors.surface.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Unlock bonus games with Bonus Game Keys!',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textPrimary,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.vpn_key,
                          color: NeonColors.highlights,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Your Keys: $_bonusGameKeys',
                          style: GoogleFonts.orbitron(
                            color: NeonColors.highlights,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Each game requires ${GameConstants.bonusGameKeysRequired} keys to unlock',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Get keys from the Lucky Wheel or buy them for ${GameConstants.bonusGameKeyUnlockCost} tokens each',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Consumer<GameProvider>(
                      builder: (context, gameProvider, child) {
                        final canBuyKey = gameProvider.tokens >= GameConstants.bonusGameKeyUnlockCost;
                        return NeonButton(
                          text: 'Buy Key (${GameConstants.bonusGameKeyUnlockCost} Tokens)',
                          onPressed: canBuyKey ? () => _buyBonusGameKey(gameProvider) : null,
                          glowColor: canBuyKey ? NeonColors.highlights : NeonColors.textSecondary,
                          width: 250,
                          height: 40,
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // Bonus games grid
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Responsive grid layout with smaller tiles to show more games
                    int crossAxisCount;
                    double childAspectRatio;

                    if (constraints.maxWidth < 400) {
                      crossAxisCount = 2; // Two columns on small screens
                      childAspectRatio = 0.85;
                    } else if (constraints.maxWidth < 600) {
                      crossAxisCount = 3; // Three columns on normal phones
                      childAspectRatio = 0.8;
                    } else if (constraints.maxWidth < 900) {
                      crossAxisCount = 4; // Four columns on tablets
                      childAspectRatio = 0.85;
                    } else {
                      crossAxisCount = 5; // Five columns on large screens
                      childAspectRatio = 0.9;
                    }

                    // Sort games: pinned first, then unpinned
                    final sortedGames = [...bonusGames];
                    sortedGames.sort((a, b) {
                      final aPinned = _pinnedGames.contains(a.id);
                      final bPinned = _pinnedGames.contains(b.id);
                      if (aPinned && !bPinned) return -1;
                      if (!aPinned && bPinned) return 1;
                      return 0; // Keep original order for same pin status
                    });

                    return GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        childAspectRatio: childAspectRatio,
                        crossAxisSpacing: constraints.maxWidth * 0.02, // 2% spacing for more compact layout
                        mainAxisSpacing: constraints.maxWidth * 0.02,
                      ),
                      itemCount: sortedGames.length,
                      itemBuilder: (context, index) {
                        final game = sortedGames[index];
                        final isPinned = _pinnedGames.contains(game.id);
                        return _buildBonusGameCard(game, isPinned);
                      },
                    );
                  },
                ),
              ),

              // Bottom padding to prevent clipping under system nav buttons
              SizedBox(height: GameConstants.bottomSystemNavPadding),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBonusGameCard(BonusGame game, bool isPinned) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final isUnlocked = gameProvider.isGameUnlocked(game.id);
        final canAfford = _bonusGameKeys >= game.keyCost;

        return GestureDetector(
          onTap: isUnlocked
              ? () => _playBonusGame(game)
              : canAfford
                  ? () => _unlockGame(game, gameProvider)
                  : null,
          child: Container(
            decoration: BoxDecoration(
              color: NeonColors.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isUnlocked
                    ? NeonColors.primaryAccent
                    : canAfford
                        ? NeonColors.highlights
                        : NeonColors.textSecondary.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: isUnlocked
                  ? [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ]
                  : canAfford
                      ? [
                          BoxShadow(
                            color: NeonColors.highlights.withValues(alpha: 0.2),
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ]
                      : null,
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                  Icon(
                    game.icon,
                    color: isUnlocked
                        ? NeonColors.primaryAccent
                        : canAfford
                            ? NeonColors.highlights
                            : NeonColors.textSecondary,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    game.name,
                    style: GoogleFonts.orbitron(
                      color: isUnlocked
                          ? NeonColors.textPrimary
                          : canAfford
                              ? NeonColors.textPrimary
                              : NeonColors.textSecondary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    game.description,
                    style: GoogleFonts.orbitron(
                      color: isUnlocked
                          ? NeonColors.textSecondary
                          : canAfford
                              ? NeonColors.textSecondary
                              : NeonColors.textSecondary.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  if (!isUnlocked) ...[
                    // Trial button section
                    if (_getRemainingTrials(game.id) > 0) ...[
                      GestureDetector(
                        onTap: () => _playTrialGame(game),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: NeonColors.primaryAccent.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: NeonColors.primaryAccent,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.play_circle_outline,
                                color: NeonColors.primaryAccent,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Trial (${_getRemainingTrials(game.id)} left)',
                                style: GoogleFonts.orbitron(
                                  color: NeonColors.primaryAccent,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: canAfford
                            ? NeonColors.highlights.withValues(alpha: 0.2)
                            : NeonColors.error.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: canAfford ? NeonColors.highlights : NeonColors.error,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.vpn_key,
                            color: canAfford ? NeonColors.highlights : NeonColors.error,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${game.keyCost} Keys',
                            style: GoogleFonts.orbitron(
                              color: canAfford ? NeonColors.highlights : NeonColors.error,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (canAfford) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Tap to unlock',
                        style: GoogleFonts.orbitron(
                          color: NeonColors.highlights,
                          fontSize: 10,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ] else ...[
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: NeonColors.success.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: NeonColors.success,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: NeonColors.success,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Unlocked',
                            style: GoogleFonts.orbitron(
                              color: NeonColors.success,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // Pin/Unpin button in top-right corner
            Positioned(
              top: 8,
              right: 8,
              child: GestureDetector(
                onTap: () => _togglePin(game.id),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: isPinned
                        ? NeonColors.primaryAccent.withValues(alpha: 0.8)
                        : NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isPinned
                          ? NeonColors.primaryAccent
                          : NeonColors.textSecondary.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    isPinned ? Icons.push_pin : Icons.push_pin_outlined,
                    color: isPinned
                        ? NeonColors.surface
                        : NeonColors.textSecondary,
                    size: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
          ),
        ).animate().fadeIn(duration: 600.ms).slideY(
          begin: 0.3,
          duration: 600.ms,
          curve: Curves.easeOutBack,
        );
      },
    );
  }

  void _unlockGame(BonusGame game, GameProvider gameProvider) async {
    final success = await gameProvider.unlockBonusGame(game.id);

    if (success && mounted) {
      // Refresh key count
      await _loadBonusGameKeys();

      // Show success dialog
      if (mounted) {
        showDialog(
          context: context,
        builder: (context) => AlertDialog(
          backgroundColor: NeonColors.surface,
          title: Text(
            'Game Unlocked!',
            style: GoogleFonts.orbitron(
              color: NeonColors.success,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                color: NeonColors.success,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                '${game.name} is now available to play!',
                style: GoogleFonts.orbitron(
                  color: NeonColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _playBonusGame(game);
              },
              child: Text(
                'Play Now',
                style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Later',
                style: GoogleFonts.orbitron(color: NeonColors.textSecondary),
              ),
            ),
          ],
        ),
      );
      }
    }
  }

  void _playBonusGame(BonusGame game) {
    _navigateToGame(game, isTrialMode: false);
  }
}

/// Model for bonus games
class BonusGame {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final int keyCost;
  final bool unlocked;

  const BonusGame({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.keyCost,
    this.unlocked = false,
  });
}
