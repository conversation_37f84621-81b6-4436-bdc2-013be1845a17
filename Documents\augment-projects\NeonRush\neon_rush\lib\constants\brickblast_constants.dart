/// Constants for BrickBlast game configuration
class BrickBlastConstants {
  // Game Configuration
  static const String gameTitle = 'BrickBlast';
  static const String gameDescription = 'Ball vs Bricks - Token Farming Game';
  
  // Ball Configuration
  static const int baseBallHP = 30;
  static const int ballHPIncreasePerRound = 5;
  static const double ballRadius = 8.0;
  static const double ballSpeed = 300.0;
  
  // Brick Configuration
  static const double brickWidth = 40.0;
  static const double brickHeight = 20.0;
  static const double brickSpacing = 5.0;
  static const int baseRows = 3;
  static const int additionalRowsPerFiveRounds = 1;
  
  // Game Area
  static const double gameWidth = 400.0;
  static const double gameHeight = 600.0;
  static const double bottomThreshold = 550.0;
  
  // Token Rewards
  static const Map<String, int> brickTokenRewards = {
    'normal': 1,
    'strong': 2,
    'reinforced': 3,
    'explosive': 2,
    'shield': 5,
  };
  
  // Score Multipliers
  static const int baseScoreMultiplier = 10; // Base score is 10x token reward
  static const double comboMultiplier = 0.1; // 10% bonus per combo
  static const double roundMultiplier = 0.05; // 5% bonus per round
  
  // Bonus Rewards
  static const int fullClearBonusTokens = 10;
  static const int fullClearBonusScore = 100;
  static const int comboThresholdForTokenBonus = 3;
  static const int highComboThresholdForTokenBonus = 5;
  static const int comboTokenBonus = 1;
  static const int highComboTokenBonus = 2;
  
  // Round Progression
  static const int survivalRoundsForBonus = 5;
  static const int survivalBonusTokens = 10;
  
  // Brick HP Scaling
  static const Map<String, int> baseBrickHP = {
    'normal': 5,
    'strong': 10,
    'reinforced': 15,
    'explosive': 8,
    'shield': 20,
  };
  
  static const Map<String, int> brickHPIncreasePerRound = {
    'normal': 2,
    'strong': 3,
    'reinforced': 4,
    'explosive': 2,
    'shield': 5,
  };
  
  // Visual Effects
  static const int maxTrailLength = 10;
  static const double particleLifetime = 2.0;
  static const int particlesPerDestruction = 15;
  static const double shakeIntensityMax = 1.0;
  static const double shakeDuration = 0.5;
  
  // Animation Timings
  static const double fadeOutDuration = 1.0;
  static const double floatingTextDuration = 1.0;
  static const double particleMaxLifetime = 2.0;
  
  // Power-ups (for future implementation)
  static const Map<String, Map<String, dynamic>> powerUpConfigs = {
    'ballSplitter': {
      'name': 'Ball Splitter',
      'description': 'Split ball into two paths',
      'cost': 50,
      'duration': 0, // Instant effect
    },
    'piercingShot': {
      'name': 'Piercing Shot',
      'description': 'No HP cost for one brick',
      'cost': 30,
      'duration': 0, // Single use
    },
    'hpBoost': {
      'name': 'HP Boost',
      'description': 'Add +10 to next ball',
      'cost': 25,
      'duration': 0, // Single use
    },
    'multiShot': {
      'name': 'Multi Shot',
      'description': 'Launch multiple balls',
      'cost': 75,
      'duration': 0, // Single use
    },
    'explosive': {
      'name': 'Explosive Ball',
      'description': 'Ball explodes on impact',
      'cost': 100,
      'duration': 0, // Single use
    },
  };
  
  // Achievement Thresholds
  static const Map<String, int> achievementThresholds = {
    'bricks_destroyed_100': 100,
    'bricks_destroyed_500': 500,
    'bricks_destroyed_1000': 1000,
    'bricks_destroyed_5000': 5000,
    'tokens_earned_500': 500,
    'tokens_earned_2000': 2000,
    'tokens_earned_10000': 10000,
    'rounds_reached_10': 10,
    'rounds_reached_25': 25,
    'rounds_reached_50': 50,
    'rounds_reached_100': 100,
    'high_score_1000': 1000,
    'high_score_5000': 5000,
    'high_score_25000': 25000,
    'games_played_10': 10,
    'games_played_50': 50,
    'games_played_100': 100,
  };
  
  // Achievement Rewards
  static const Map<String, int> achievementTokenRewards = {
    'bricks_destroyed_100': 50,
    'bricks_destroyed_500': 150,
    'bricks_destroyed_1000': 300,
    'bricks_destroyed_5000': 1000,
    'tokens_earned_500': 25,
    'tokens_earned_2000': 100,
    'tokens_earned_10000': 500,
    'rounds_reached_10': 100,
    'rounds_reached_25': 250,
    'rounds_reached_50': 500,
    'rounds_reached_100': 1000,
    'high_score_1000': 50,
    'high_score_5000': 200,
    'high_score_25000': 1000,
    'games_played_10': 25,
    'games_played_50': 100,
    'games_played_100': 250,
  };
  
  // Storage Keys
  static const String progressKey = 'brickblast_progress';
  static const String settingsKey = 'brickblast_settings';
  static const String achievementsKey = 'brickblast_achievements';
  
  // Game Balance
  static const double maxGameSpeed = 2.0;
  static const double minGameSpeed = 0.5;
  static const int maxBallsPerRound = 5;
  static const int maxBricksPerRow = 10;
  static const int maxRows = 15;
  
  // UI Constants
  static const double uiPadding = 10.0;
  static const double buttonHeight = 50.0;
  static const double iconSize = 24.0;
  static const double textSizeLarge = 24.0;
  static const double textSizeMedium = 16.0;
  static const double textSizeSmall = 12.0;
  
  // Colors (referencing NeonColors from main constants)
  static const String primaryColorName = 'primaryAccent';
  static const String secondaryColorName = 'highlights';
  static const String successColorName = 'neonGreen';
  static const String warningColorName = 'orangeFlame';
  static const String errorColorName = 'error';
  
  // Sound Effects (for future implementation)
  static const Map<String, String> soundEffects = {
    'ballLaunch': 'ball_launch.wav',
    'brickHit': 'brick_hit.wav',
    'brickDestroy': 'brick_destroy.wav',
    'ballDepleted': 'ball_depleted.wav',
    'roundComplete': 'round_complete.wav',
    'gameOver': 'game_over.wav',
    'tokenEarned': 'token_earned.wav',
    'powerUpActivated': 'powerup_activated.wav',
    'achievementUnlocked': 'achievement_unlocked.wav',
  };
  
  // Game Tips
  static const List<String> gameTips = [
    'Aim for bricks with lower HP to conserve ball energy',
    'Try to hit multiple bricks in a chain for combo bonuses',
    'Shield bricks give the most tokens but are hardest to break',
    'Explosive bricks can damage nearby bricks when destroyed',
    'Plan your shots to maximize brick destruction per ball',
    'Higher rounds give better score multipliers',
    'Full clear bonuses are worth the extra effort',
    'Watch your ball HP - it decreases with each brick hit',
  ];
  
  // Tutorial Steps
  static const List<Map<String, String>> tutorialSteps = [
    {
      'title': 'Welcome to BrickBlast!',
      'description': 'Destroy bricks with your ball to earn tokens.',
    },
    {
      'title': 'Ball HP System',
      'description': 'Your ball has HP that decreases when hitting bricks.',
    },
    {
      'title': 'Aiming',
      'description': 'Tap or drag to aim your ball upward.',
    },
    {
      'title': 'Brick Types',
      'description': 'Different colored bricks have different HP and rewards.',
    },
    {
      'title': 'Token Rewards',
      'description': 'Earn tokens for each brick destroyed and bonus rewards.',
    },
    {
      'title': 'Round Progression',
      'description': 'Complete rounds to face tougher bricks and earn more tokens.',
    },
  ];
}
