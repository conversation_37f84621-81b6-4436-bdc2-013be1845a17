import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'dart:async';

import '../providers/breakfinity_provider.dart';
import '../constants/breakfinity_constants.dart';
import '../core/constants.dart' as core_constants;
import '../ui/neon_button.dart';
import '../ui/neon_text.dart';
import '../models/breakfinity_section.dart';

import '../providers/game_provider.dart';
import '../services/token_service.dart';
import '../models/token_transaction.dart';
import '../ui/particle_system.dart';

import '../widgets/breakfinity_section_renderer.dart';
import '../widgets/breakfinity_reward_display.dart';
import '../widgets/flame_particle_widget.dart';
import '../models/breakfinity_layer_theme.dart';

/// Represents floating text for reward notifications
class FloatingText {
  String text;
  Offset position;
  Offset velocity;
  Color color;
  double fontSize;
  double opacity;
  DateTime createdAt;

  FloatingText({
    required this.text,
    required this.position,
    required this.velocity,
    required this.color,
    required this.fontSize,
    this.opacity = 1.0,
    required this.createdAt,
  });
}

/// Main screen for the Breakfinity game
class BreakfinityScreen extends StatefulWidget {
  const BreakfinityScreen({super.key});

  @override
  State<BreakfinityScreen> createState() => _BreakfinityScreenState();
}

class _BreakfinityScreenState extends State<BreakfinityScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late AnimationController _backgroundController;
  final List<Particle> _particles = [];

  final GlobalKey _rewardOverlayKey = GlobalKey();
  final List<FloatingText> _floatingTexts = [];
  final List<FlameParticleEffect> _flameParticleEffects = [];
  Timer? _floatingTextTimer;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    // Initialize the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<BreakfinityProvider>();
      final gameProvider = context.read<GameProvider>();

      // Set GameProvider reference for token synchronization
      provider.setGameProvider(gameProvider);

      // Disabled particle effect to fix strange dots issue
      // provider.onParticleEffect = _createParticleEffect;
      // Enable reward callback with floating text implementation
      provider.onRewardDiscovered = _showFloatingReward;
      // Removed enhanced reward popup to eliminate annoying popups
      // provider.onEnhancedRewardDiscovered = _showEnhancedReward;
      provider.initialize();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _glowController.dispose();
    _backgroundController.dispose();
    _floatingTextTimer?.cancel();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return BreakfinityRewardOverlay(
      key: _rewardOverlayKey,
      child: Scaffold(
      backgroundColor: BreakfinityConstants.backgroundColor,
      body: SafeArea(
        child: Consumer<BreakfinityProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return _buildLoadingScreen();
          }

          return Stack(
            children: [
              // Background gradient
              _buildBackground(),
              
              // Main game area with proper system padding
              Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + core_constants.GameConstants.topSystemPadding,
                  bottom: MediaQuery.of(context).padding.bottom + core_constants.GameConstants.bottomSystemNavPadding,
                  left: core_constants.GameConstants.sideSystemPadding,
                  right: core_constants.GameConstants.sideSystemPadding,
                ),
                child: Column(
                  children: [
                    // Header with stats
                    _buildHeader(provider),

                    // Game viewport
                    Expanded(
                      child: _buildGameViewport(provider),
                    ),

                    // Bottom controls
                    _buildBottomControls(provider),
                  ],
                ),
              ),
              
              // Active power-ups overlay
              _buildPowerUpsOverlay(provider),

              // Floating text overlay
              _buildFloatingTextOverlay(),
            ],
          );
        },
        ),
      ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: BreakfinityConstants.glowColor,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: BreakfinityConstants.glowColor.withValues(alpha: 0.5),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(BreakfinityConstants.glowColor),
              strokeWidth: 3,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
              .scale(duration: 1.seconds, curve: Curves.easeInOut)
              .then()
              .scale(begin: const Offset(1.2, 1.2), end: const Offset(1.0, 1.0)),
          
          const SizedBox(height: 24),
          
          NeonText(
            'LOADING BREAKFINITY',
            fontSize: 24,
            textColor: BreakfinityConstants.glowColor,
            glowColor: BreakfinityConstants.glowColor,
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF000020), // Dark blue like BrickBlast
                Color(0xFF000040), // Slightly lighter blue
                Color(0xFF000020), // Back to dark blue
              ],
              stops: [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: ParticlePainter(_particles),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  Widget _buildHeader(BreakfinityProvider provider) {
    final progress = provider.progress;

    return Container(
      height: BreakfinityConstants.headerHeight,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: BreakfinityConstants.structureColor.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(
            color: BreakfinityConstants.glowColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back,
              color: BreakfinityConstants.glowColor,
            ),
          ),

          const SizedBox(width: 8),

          // Layer info - flexible to prevent overflow
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: NeonText(
                    'LAYER ${progress.currentLayer}',
                    fontSize: 20,
                    textColor: BreakfinityConstants.glowColor,
                    glowColor: BreakfinityConstants.glowColor,
                  ),
                ),

                const SizedBox(height: 4),

                // Progress bar
                Container(
                  height: 6,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3),
                    color: BreakfinityConstants.structureColor,
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: provider.progress.getCurrentLayerProgress(),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        color: BreakfinityConstants.glowColor,
                        boxShadow: [
                          BoxShadow(
                            color: BreakfinityConstants.glowColor.withValues(alpha: 0.5),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // Stats - flexible to prevent overflow
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: NeonText(
                    'DMG: ${progress.getEffectiveDamage()}',
                    fontSize: 14,
                    textColor: BreakfinityConstants.destroyColor,
                    glowColor: BreakfinityConstants.destroyColor,
                  ),
                ),

                const SizedBox(height: 4),

                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: NeonText(
                    'TAPS: ${progress.totalTaps}',
                    fontSize: 12,
                    textColor: Colors.white.withValues(alpha: 0.7),
                    glowColor: Colors.white.withValues(alpha: 0.3),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameViewport(BreakfinityProvider provider) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              provider.currentLayerTheme.primaryColor.withValues(alpha: 0.1),
              provider.currentLayerTheme.secondaryColor.withValues(alpha: 0.1),
            ],
          ),
          border: Border.all(
            color: provider.currentLayerTheme.primaryColor.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: Stack(
            children: [
              // Background animation
              AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  return CustomPaint(
                    size: Size.infinite,
                    painter: _BackgroundPainter(
                      theme: provider.currentLayerTheme,
                      animationValue: _backgroundController.value,
                    ),
                  );
                },
              ),

              // Game sections with layer transition animation
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 800),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0.0, 0.3),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: _buildSections(provider),
              ),

              // Particle overlay (ignore pointer events so taps can reach sections below)
              IgnorePointer(
                child: CustomPaint(
                  size: Size.infinite,
                  painter: _ParticlePainter(_particles),
                ),
              ),

            // Flame particle overlay
            if (_flameParticleEffects.isNotEmpty)
              IgnorePointer(
                child: FlameParticleWidget(
                  size: Size.infinite,
                  effects: _flameParticleEffects,
                ),
              ),

              // Reward overlay (commented out for now)
              // if (provider.showRewardOverlay)
              //   _buildRewardOverlay(provider),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSections(BreakfinityProvider provider) {
    return Container(
      key: ValueKey(provider.progress.currentLayer), // Key for AnimatedSwitcher
      child: _buildSectionGrid(provider),
    );
  }



  Widget _buildSectionGrid(BreakfinityProvider provider) {
    final sections = provider.currentLayerSections.values.toList();

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate optimal grid dimensions to fill the available space
        final availableWidth = constraints.maxWidth;
        final availableHeight = constraints.maxHeight;

        // Calculate section size to fill the container completely
        final sectionsPerColumn = (BreakfinityConstants.sectionsPerLayer / BreakfinityConstants.sectionsPerRow).ceil();
        final sectionWidth = availableWidth / BreakfinityConstants.sectionsPerRow;
        final sectionHeight = availableHeight / sectionsPerColumn;
        final aspectRatio = sectionWidth / sectionHeight;

        return GridView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(), // Disable scrolling to fill space
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: BreakfinityConstants.sectionsPerRow,
            crossAxisSpacing: 0, // No spacing between sections
            mainAxisSpacing: 0,  // No spacing between sections
            childAspectRatio: aspectRatio,
          ),
          itemCount: sections.length,
          itemBuilder: (context, index) {
            final section = sections[index];
            return _buildSection(section, provider);
          },
        );
      },
    );
  }

  Widget _buildSection(BreakfinitySection section, BreakfinityProvider provider) {
    return BreakfinitySectionRenderer(
      key: ValueKey('${section.id}_${section.currentHealth}_${section.isDestroyed}'),
      section: section,
      onTap: section.isDestroyed ? null : () => _handleSectionTap(section, provider),
      showCracks: true,
      showGlow: true,
      animationProgress: _pulseController.value,
    );
  }

  /// Handle section tap with position-based token animation
  Future<void> _handleSectionTap(BreakfinitySection section, BreakfinityProvider provider) async {
    // Get the section's position before tapping
    final sectionPosition = section.position;
    final sectionSize = section.size;

    // Calculate center position of the section for floating text
    final centerPosition = Offset(
      sectionPosition.dx + sectionSize.width / 2,
      sectionPosition.dy + sectionSize.height / 2,
    );

    // Store the old health to check if section will be destroyed
    final oldHealth = section.currentHealth;
    final damage = provider.progress.getEffectiveDamage();
    final willBeDestroyed = oldHealth <= damage;

    // Add Flame particle effect for tap
    _addFlameParticleEffect(
      FlameParticleType.tap,
      centerPosition,
      provider.currentLayerTheme.primaryColor,
      0.5,
    );

    // Perform the tap
    await provider.tapSection(section.id);

    // Show break particles if section was destroyed
    if (willBeDestroyed) {
      // Add enhanced break particle effect
      _addFlameParticleEffect(
        FlameParticleType.breakEffect,
        centerPosition,
        provider.currentLayerTheme.secondaryColor,
        1.2,
      );

      // Note: Token rewards are handled by the provider, no need for duplicate animation
    }
  }





  Widget _buildBottomControls(BreakfinityProvider provider) {
    return Container(
      height: BreakfinityConstants.bottomPanelHeight,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: BreakfinityConstants.structureColor.withValues(alpha: 0.3),
        border: Border(
          top: BorderSide(
            color: BreakfinityConstants.glowColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Power-ups button
          Expanded(
            child: NeonButton(
              text: 'POWER-UPS',
              onPressed: () => _showPowerUpsDialog(provider),
              glowColor: BreakfinityConstants.glowColor,
            ),
          ),

          const SizedBox(width: 16),

          // Upgrades button
          Expanded(
            child: NeonButton(
              text: 'UPGRADES',
              onPressed: () => _showUpgradesDialog(provider),
              glowColor: BreakfinityConstants.destroyColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPowerUpsOverlay(BreakfinityProvider provider) {
    if (provider.activePowerUps.isEmpty) return const SizedBox.shrink();
    
    return Positioned(
      top: 100,
      right: 16,
      child: Column(
        children: provider.activePowerUps.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: BreakfinityConstants.glowColor.withValues(alpha: 0.2),
              border: Border.all(
                color: BreakfinityConstants.glowColor,
                width: 1,
              ),
            ),
            child: NeonText(
              entry.key.toUpperCase(),
              fontSize: 12,
              textColor: BreakfinityConstants.glowColor,
              glowColor: BreakfinityConstants.glowColor,
            ),
          );
        }).toList(),
      ),
    );
  }

  void _showPowerUpsDialog(BreakfinityProvider provider) {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentTokens = gameProvider.tokens;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: BreakfinityConstants.structureColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: BreakfinityConstants.glowColor,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: BreakfinityConstants.glowColor.withValues(alpha: 0.5),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Container(
                padding: const EdgeInsets.all(20),
                child: NeonText(
                  'POWER-UPS',
                  fontSize: 24,
                  textColor: BreakfinityConstants.glowColor,
                  glowColor: BreakfinityConstants.glowColor,
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      _buildPowerUpCard(
                        'Mega Tap',
                        'Increases tap damage by 5x for 30 seconds',
                        Icons.flash_on,
                        const Color(0xFFFFD93D),
                        100,
                        currentTokens,
                        provider.activePowerUps.containsKey('mega_tap'),
                        () => _activatePowerUp(provider, 'mega_tap', 100),
                      ),
                      const SizedBox(height: 12),
                      _buildPowerUpCard(
                        'Auto-Tapper',
                        'Automatically taps sections for 30 seconds',
                        Icons.touch_app,
                        const Color(0xFF9B59B6),
                        150,
                        currentTokens,
                        provider.activePowerUps.containsKey('auto_tapper'),
                        () => _activatePowerUp(provider, 'auto_tapper', 150),
                      ),
                      const SizedBox(height: 12),
                      _buildPowerUpCard(
                        'Layer Bomb',
                        'Destroys 25% of remaining sections in current layer',
                        Icons.flash_on,
                        const Color(0xFFE74C3C),
                        500,
                        currentTokens,
                        false, // Instant effect
                        () => _activatePowerUp(provider, 'layer_bomb', 500),
                      ),
                    ],
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                child: NeonButton(
                  text: 'CLOSE',
                  onPressed: () => Navigator.of(context).pop(),
                  glowColor: BreakfinityConstants.glowColor,
                  height: 40,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUpgradesDialog(BreakfinityProvider provider) {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentTokens = gameProvider.tokens;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: BreakfinityConstants.structureColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: BreakfinityConstants.destroyColor,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: BreakfinityConstants.destroyColor.withValues(alpha: 0.5),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Container(
                padding: const EdgeInsets.all(20),
                child: NeonText(
                  'UPGRADES',
                  fontSize: 24,
                  textColor: BreakfinityConstants.destroyColor,
                  glowColor: BreakfinityConstants.destroyColor,
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      _buildUpgradeCard(
                        'Mega Tap',
                        'Increase Mega Tap power-up damage permanently',
                        Icons.flash_on,
                        BreakfinityConstants.destroyColor,
                        _getUpgradeLevel(provider, 'damage_boost'),
                        _calculateUpgradeCost(_getUpgradeLevel(provider, 'damage_boost'), 100),
                        currentTokens,
                        () => _purchaseUpgrade(provider, 'damage_boost', 100),
                      ),
                      const SizedBox(height: 16),
                      _buildUpgradeCard(
                        'Auto-Tapper',
                        'Increase auto-tapper frequency and effectiveness',
                        Icons.touch_app,
                        const Color(0xFF9B59B6),
                        _getUpgradeLevel(provider, 'auto_tapper'),
                        _calculateUpgradeCost(_getUpgradeLevel(provider, 'auto_tapper'), 200),
                        currentTokens,
                        () => _purchaseUpgrade(provider, 'auto_tapper', 200),
                      ),
                    ],
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                child: NeonButton(
                  text: 'CLOSE',
                  onPressed: () => Navigator.of(context).pop(),
                  glowColor: BreakfinityConstants.destroyColor,
                  height: 40,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPowerUpCard(
    String name,
    String description,
    IconData icon,
    Color color,
    int cost,
    int currentTokens,
    bool isActive,
    VoidCallback onActivate, {
    int? level,
  }) {
    final canAfford = currentTokens >= cost;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isActive
            ? color.withValues(alpha: 0.3)
            : BreakfinityConstants.structureColor.withValues(alpha: 0.2),
        border: Border.all(
          color: isActive
              ? color
              : BreakfinityConstants.glowColor.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: NeonText(
                  name,
                  fontSize: 16,
                  textColor: color,
                  glowColor: color,
                ),
              ),
              if (level != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: color.withValues(alpha: 0.3),
                    border: Border.all(color: color),
                  ),
                  child: NeonText(
                    'LV $level',
                    fontSize: 10,
                    textColor: color,
                    glowColor: color,
                  ),
                ),
              if (isActive)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.green.withValues(alpha: 0.3),
                    border: Border.all(color: Colors.green),
                  ),
                  child: NeonText(
                    'ACTIVE',
                    fontSize: 10,
                    textColor: Colors.green,
                    glowColor: Colors.green,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          NeonText(
            description,
            fontSize: 12,
            textColor: Colors.white.withValues(alpha: 0.8),
            glowColor: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonText(
                'Cost: $cost tokens',
                fontSize: 12,
                textColor: canAfford ? Colors.yellow : Colors.red,
                glowColor: canAfford ? Colors.yellow.withValues(alpha: 0.5) : Colors.red.withValues(alpha: 0.5),
              ),
              SizedBox(
                width: 100,
                child: NeonButton(
                  text: isActive ? 'ACTIVE' : 'ACTIVATE',
                  onPressed: isActive || !canAfford ? null : onActivate,
                  glowColor: color,
                  height: 32,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradeCard(
    String name,
    String description,
    IconData icon,
    Color color,
    double currentLevel,
    int cost,
    int currentTokens,
    VoidCallback onUpgrade,
  ) {
    final canAfford = currentTokens >= cost;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: BreakfinityConstants.structureColor.withValues(alpha: 0.2),
        border: Border.all(
          color: color.withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: NeonText(
                  name,
                  fontSize: 16,
                  textColor: color,
                  glowColor: color,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: color.withValues(alpha: 0.3),
                  border: Border.all(color: color),
                ),
                child: NeonText(
                  'LV ${currentLevel.toInt()}',
                  fontSize: 10,
                  textColor: color,
                  glowColor: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          NeonText(
            description,
            fontSize: 12,
            textColor: Colors.white.withValues(alpha: 0.8),
            glowColor: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonText(
                'Cost: $cost tokens',
                fontSize: 12,
                textColor: canAfford ? Colors.yellow : Colors.red,
                glowColor: canAfford ? Colors.yellow.withValues(alpha: 0.5) : Colors.red.withValues(alpha: 0.5),
              ),
              SizedBox(
                width: 100,
                child: NeonButton(
                  text: 'UPGRADE',
                  onPressed: canAfford ? onUpgrade : null,
                  glowColor: color,
                  height: 32,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _activatePowerUp(BreakfinityProvider provider, String powerUpId, int cost) async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);

    if (gameProvider.tokens >= cost) {
      final success = await TokenService.spendTokens(
        amount: cost,
        type: TokenTransactionType.powerUpPurchase,
        description: 'Breakfinity power-up: $powerUpId',
      );

      if (success) {
        await provider.activatePowerUp(powerUpId);
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  double _getUpgradeLevel(BreakfinityProvider provider, String upgradeId) {
    return (provider.progress.upgrades[upgradeId] as num?)?.toDouble() ?? 0.0;
  }

  int _calculateUpgradeCost(double currentLevel, int baseCost) {
    return (baseCost * (1.5 * (currentLevel + 1))).round();
  }

  Future<void> _purchaseUpgrade(BreakfinityProvider provider, String upgradeId, int baseCost) async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentLevel = _getUpgradeLevel(provider, upgradeId);
    final cost = _calculateUpgradeCost(currentLevel, baseCost);

    if (gameProvider.tokens >= cost) {
      final success = await TokenService.spendTokens(
        amount: cost,
        type: TokenTransactionType.powerUpUpgrade,
        description: 'Breakfinity upgrade: $upgradeId',
      );

      if (success) {
        // Apply the upgrade
        provider.purchaseUpgrade(upgradeId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Upgrade purchased successfully!'),
              backgroundColor: BreakfinityConstants.glowColor,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    }
  }
  /// Show floating reward text (tokens are handled by provider to avoid duplication)
  void _showFloatingReward(String message, Map<String, dynamic> rewards) {
    // Token rewards are now handled by the provider to prevent duplication
    // Only show non-token rewards here if needed in the future
  }

  // Removed _showFloatingTokenFromPosition to prevent duplicate token animations

  /// Add Flame particle effect
  void _addFlameParticleEffect(
    FlameParticleType type,
    Offset position,
    Color color,
    double lifetime,
  ) {
    final effect = FlameParticleEffect(
      type: type,
      position: position,
      color: color,
      lifetime: lifetime,
    );

    setState(() {
      _flameParticleEffects.add(effect);
    });

    // Remove effect after its lifetime
    Future.delayed(Duration(milliseconds: (lifetime * 1000).round()), () {
      if (mounted) {
        setState(() {
          _flameParticleEffects.remove(effect);
        });
      }
    });
  }



  /// Build floating text overlay
  Widget _buildFloatingTextOverlay() {
    return IgnorePointer(
      child: Stack(
        children: _floatingTexts.map((text) {
          return Positioned(
            left: text.position.dx,
            top: text.position.dy,
            child: Opacity(
              opacity: text.opacity,
              child: Text(
                text.text,
                style: TextStyle(
                  color: text.color,
                  fontSize: text.fontSize,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: text.color.withValues(alpha: 0.8),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Custom painter for drawing cracks on sections
class CrackPainter extends CustomPainter {
  final List<Offset> cracks;
  final double progress;

  CrackPainter({required this.cracks, required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = BreakfinityConstants.crackColor.withValues(alpha: progress)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < cracks.length; i++) {
      if (i / cracks.length <= progress) {
        final start = cracks[i];
        final end = i < cracks.length - 1 
            ? cracks[i + 1] 
            : Offset(start.dx + 10, start.dy + 10);
        
        canvas.drawLine(start, end, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CrackPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.cracks != cracks;
  }
}

/// Custom painter for background effects
class _BackgroundPainter extends CustomPainter {
  final BreakfinityLayerTheme theme;
  final double animationValue;

  _BackgroundPainter({
    required this.theme,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.primaryColor.withValues(alpha: 0.1),
          theme.secondaryColor.withValues(alpha: 0.1),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for particle effects
class _ParticlePainter extends CustomPainter {
  final List<Particle> particles;

  _ParticlePainter(this.particles);

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      final paint = Paint()
        ..color = particle.color.withValues(alpha: particle.alpha)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        particle.position,
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
