import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';

import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// Neon Stack - Drop moving blocks to stack them as high as possible
class NeonStackGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const NeonStackGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<NeonStackGame> createState() => _NeonStackGameState();
}

class _NeonStackGameState extends State<NeonStackGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late AnimationController _blockController;
  late Animation<double> _blockAnimation;
  
  final List<StackBlock> _blocks = [];
  StackBlock? _currentBlock;
  int _score = 0;
  bool _gameActive = false;
  bool _gameStarted = false;
  double _blockWidth = 200.0;
  final double _blockHeight = 40.0;
  double _stackHeight = 0.0;
  int _level = 1;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _blockController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _blockController = AnimationController(
      duration: Duration(milliseconds: 2000 - (_level * 100).clamp(0, 1500)),
      vsync: this,
    );

    _blockAnimation = Tween<double>(
      begin: -_blockWidth,
      end: MediaQuery.of(context).size.width,
    ).animate(CurvedAnimation(
      parent: _blockController,
      curve: Curves.linear,
    ));

    _blockController.addListener(() {
      if (_currentBlock != null) {
        setState(() {
          _currentBlock = _currentBlock!.copyWith(
            x: _blockAnimation.value,
          );
        });
      }
    });

    _blockController.addStatusListener((status) {
      if (status == AnimationStatus.completed && _gameActive) {
        _blockController.reverse();
      } else if (status == AnimationStatus.dismissed && _gameActive) {
        _blockController.forward();
      }
    });
  }

  void _startNewGame() {
    setState(() {
      _blocks.clear();
      _score = 0;
      _gameActive = true;
      _gameStarted = true;
      _blockWidth = 200.0;
      _stackHeight = 0.0;
      _level = 1;
    });

    // Add base block
    final screenWidth = MediaQuery.of(context).size.width;
    final baseBlock = StackBlock(
      x: (screenWidth - _blockWidth) / 2,
      y: MediaQuery.of(context).size.height - 200,
      width: _blockWidth,
      height: _blockHeight,
      color: const Color(0xFF00FFFF),
    );
    _blocks.add(baseBlock);
    _stackHeight = baseBlock.y;

    _spawnNewBlock();
  }

  void _spawnNewBlock() {
    if (!_gameActive) return;

    final colors = [
      const Color(0xFF00FFFF),
      const Color(0xFFFF00FF),
      const Color(0xFFFFFF00),
      const Color(0xFF00FF00),
      const Color(0xFFFF6B35),
    ];

    setState(() {
      _currentBlock = StackBlock(
        x: -_blockWidth,
        y: _stackHeight - _blockHeight,
        width: _blockWidth,
        height: _blockHeight,
        color: colors[Random().nextInt(colors.length)],
      );
    });

    _blockController.forward();
  }

  void _dropBlock() async {
    if (!_gameActive || _currentBlock == null) return;

    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();

    _blockController.stop();

    final droppedBlock = _currentBlock!;
    final lastBlock = _blocks.last;

    // Calculate overlap
    final leftEdge = max(droppedBlock.x, lastBlock.x);
    final rightEdge = min(
      droppedBlock.x + droppedBlock.width,
      lastBlock.x + lastBlock.width,
    );
    final overlap = rightEdge - leftEdge;

    if (overlap <= 0) {
      // No overlap - game over
      _endGame();
      return;
    }

    // Trim the block to the overlap area
    final trimmedBlock = droppedBlock.copyWith(
      x: leftEdge,
      width: overlap,
    );

    setState(() {
      _blocks.add(trimmedBlock);
      _currentBlock = null;
      _score++;
      _blockWidth = overlap;
      _stackHeight -= _blockHeight;
      
      // Increase level every 5 blocks
      if (_score % 5 == 0) {
        _level++;
        _updateSpeed();
      }
    });

    // Check if block is too small to continue
    if (_blockWidth < 30) {
      _endGame();
      return;
    }

    // Spawn next block after a short delay
    Timer(const Duration(milliseconds: 500), () {
      if (_gameActive) {
        _spawnNewBlock();
      }
    });
  }

  void _updateSpeed() {
    _blockController.duration = Duration(
      milliseconds: (2000 - (_level * 100)).clamp(500, 2000),
    );
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    _blockController.stop();
    await NeonHaptics.gameOver();

    if (!mounted) return;

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score * 5); // 5 tokens per block

    // Call the completion callback instead of showing dialog directly
    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, _score * 5);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onTap: _dropBlock,
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: CustomPaint(
                  painter: NeonStackPainter(
                    blocks: _blocks,
                    currentBlock: _currentBlock,
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: NeonText.body(
                    'TAP TO DROP BLOCK',
                    glowColor: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a block in the stack
class StackBlock {
  final double x;
  final double y;
  final double width;
  final double height;
  final Color color;

  const StackBlock({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
  });

  StackBlock copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    Color? color,
  }) {
    return StackBlock(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      color: color ?? this.color,
    );
  }
}

/// Custom painter for the neon stack game
class NeonStackPainter extends CustomPainter {
  final List<StackBlock> blocks;
  final StackBlock? currentBlock;

  NeonStackPainter({
    required this.blocks,
    this.currentBlock,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw stacked blocks
    for (final block in blocks) {
      _drawBlock(canvas, block);
    }

    // Draw current moving block
    if (currentBlock != null) {
      _drawBlock(canvas, currentBlock!);
    }
  }

  void _drawBlock(Canvas canvas, StackBlock block) {
    final rect = Rect.fromLTWH(block.x, block.y, block.width, block.height);
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = block.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(8)),
      glowPaint,
    );
    
    // Draw main block
    final blockPaint = Paint()
      ..color = block.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(8)),
      blockPaint,
    );
    
    // Draw border
    final borderPaint = Paint()
      ..color = block.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(8)),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
