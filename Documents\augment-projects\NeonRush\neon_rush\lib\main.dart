import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'constants.dart';
import 'core/sound_manager.dart';
import 'providers/game_provider.dart';
import 'providers/breakfinity_provider.dart';
import 'providers/brickblast_provider.dart';
import 'providers/theme_provider.dart';
import 'managers/game_state_manager.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize sound manager
  await SoundManager().initialize();

  runApp(const TapVerseApp());
}

class TapVerseApp extends StatefulWidget {
  const TapVerseApp({super.key});

  @override
  State<TapVerseApp> createState() => _TapVerseAppState();
}

class _TapVerseAppState extends State<TapVerseApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // App is going to background or being closed
        SoundManager().pauseMusic();
        break;
      case AppLifecycleState.resumed:
        // App is coming back to foreground
        SoundManager().resumeMusic();
        break;
      case AppLifecycleState.hidden:
        // App is hidden but still running
        SoundManager().pauseMusic();
        break;
    }
  }



  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => GameStateManager(),
        ),
        ChangeNotifierProvider(
          create: (context) => GameProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => BreakfinityProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => BrickBlastProvider(),
        ),
        ChangeNotifierProxyProvider<GameStateManager, ThemeProvider>(
          create: (context) => ThemeProvider(context.read<GameStateManager>()),
          update: (context, gameStateManager, previous) =>
              previous ?? ThemeProvider(gameStateManager),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: GameConstants.gameTitle,
            theme: themeProvider.getFlutterTheme(),
            home: const InitializationWrapper(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

class InitializationWrapper extends StatefulWidget {
  const InitializationWrapper({super.key});

  @override
  State<InitializationWrapper> createState() => _InitializationWrapperState();
}

class _InitializationWrapperState extends State<InitializationWrapper> {
  late Future<void> _initializationFuture;

  @override
  void initState() {
    super.initState();
    _initializationFuture = _initializeGame();
  }

  Future<void> _initializeGame() async {
    final gameProvider = context.read<GameProvider>();
    final gameStateManager = context.read<GameStateManager>();

    await gameProvider.initialize();
    await gameStateManager.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initializationFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            backgroundColor: NeonColors.background,
            body: Center(
              child: CircularProgressIndicator(
                color: NeonColors.primaryAccent,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            backgroundColor: NeonColors.background,
            body: Center(
              child: Text(
                'Error initializing game: ${snapshot.error}',
                style: const TextStyle(color: NeonColors.error),
              ),
            ),
          );
        }

        return const NeonRushHome();
      },
    );
  }
}
