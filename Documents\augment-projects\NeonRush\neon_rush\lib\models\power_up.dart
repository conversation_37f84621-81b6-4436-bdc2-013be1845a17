import 'package:flutter/material.dart';
import '../core/constants.dart';

/// Represents a power-up that can be used in the game
class PowerUp {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final int cost;
  final Duration duration;
  final PowerUpType type;
  final Color color;
  final bool isConsumable;
  final Map<String, dynamic> effects;
  final int tier; // New: tier support (1, 2, 3)
  final int maxTier; // New: maximum tier available
  final bool isPermanent; // New: permanent upgrades

  const PowerUp({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.cost,
    required this.duration,
    required this.type,
    required this.color,
    this.isConsumable = true,
    this.effects = const {},
    this.tier = 1,
    this.maxTier = 3,
    this.isPermanent = false,
  });

  /// Creates a copy of this power-up with modified properties
  PowerUp copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    int? cost,
    Duration? duration,
    PowerUpType? type,
    Color? color,
    bool? isConsumable,
    Map<String, dynamic>? effects,
    int? tier,
    int? maxTier,
    bool? isPermanent,
  }) {
    return PowerUp(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      cost: cost ?? this.cost,
      duration: duration ?? this.duration,
      type: type ?? this.type,
      color: color ?? this.color,
      isConsumable: isConsumable ?? this.isConsumable,
      effects: effects ?? this.effects,
      tier: tier ?? this.tier,
      maxTier: maxTier ?? this.maxTier,
      isPermanent: isPermanent ?? this.isPermanent,
    );
  }

  /// Converts power-up to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon.codePoint,
      'cost': cost,
      'duration': duration.inMilliseconds,
      'type': type.name,
      'color': color.toARGB32(),
      'isConsumable': isConsumable,
      'effects': effects,
      'tier': tier,
      'maxTier': maxTier,
      'isPermanent': isPermanent,
    };
  }

  /// Creates power-up from JSON
  factory PowerUp.fromJson(Map<String, dynamic> json) {
    return PowerUp(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: IconData(json['icon'], fontFamily: 'MaterialIcons'),
      cost: json['cost'],
      duration: Duration(milliseconds: json['duration']),
      type: PowerUpType.values.firstWhere((t) => t.name == json['type']),
      color: Color(json['color']),
      isConsumable: json['isConsumable'] ?? true,
      effects: Map<String, dynamic>.from(json['effects'] ?? {}),
      tier: json['tier'] ?? 1,
      maxTier: json['maxTier'] ?? 3,
      isPermanent: json['isPermanent'] ?? false,
    );
  }

  /// Check if this power-up can be upgraded
  bool get canUpgrade => tier < maxTier && !isPermanent;

  /// Get the next tier version of this power-up
  PowerUp? getNextTier() {
    if (!canUpgrade) return null;

    final nextTier = tier + 1;
    final nextCost = GameCalculations.getPowerUpCost(id, nextTier);
    final nextEffects = GameCalculations.getPowerUpEffects(id, nextTier);

    if (nextCost == 0 || nextEffects == null) return null;

    return copyWith(
      tier: nextTier,
      cost: nextCost,
      effects: nextEffects,
      name: '$name (Tier $nextTier)',
    );
  }
}

/// Predefined power-ups with tiered system
class PowerUps {
  // Time Boost - Tier 1
  static const timeBoostT1 = PowerUp(
    id: 'time_boost',
    name: 'Time Boost',
    description: 'Adds 5 seconds to the level timer',
    icon: Icons.access_time,
    cost: 50, // Tier 1 cost
    duration: Duration(milliseconds: 100),
    type: PowerUpType.timeBoost,
    color: Color(0xFFFF8000),
    tier: 1,
    maxTier: 3,
    effects: {
      'timeAdd': 5.0,
      'instantEffect': true,
    },
  );

  // Slow Motion - Tier 1
  static const slowMotionT1 = PowerUp(
    id: 'slow_motion',
    name: 'Slow Motion',
    description: 'Slows down all targets for 5 seconds',
    icon: Icons.slow_motion_video,
    cost: 60, // Tier 1 cost
    duration: Duration(seconds: 5),
    type: PowerUpType.slowMotion,
    color: Color(0xFF00FFFF),
    tier: 1,
    maxTier: 3,
    effects: {
      'slowFactor': 0.5,
      'affectsAll': true,
    },
  );

  // Immune (formerly Shield) - Tier 1
  static const immuneT1 = PowerUp(
    id: 'immune',
    name: 'Immune',
    description: 'Immune to penalties for 5 seconds',
    icon: Icons.shield,
    cost: 175, // Tier 1 cost
    duration: Duration(seconds: 5),
    type: PowerUpType.shield,
    color: Color(0xFF00FF00),
    tier: 1,
    maxTier: 3,
    effects: {
      'immuneToAll': true,
      'glowEffect': true,
    },
  );

  // Swipe Mode - Permanent upgrade
  static const swipeMode = PowerUp(
    id: 'swipe_mode',
    name: 'Swipe Mode',
    description: 'Permanent: Swipe equals tap mode',
    icon: Icons.swipe,
    cost: 250, // Single tier cost
    duration: Duration.zero, // Permanent
    type: PowerUpType.swipeMode,
    color: Color(0xFFFFD700),
    tier: 1,
    maxTier: 1,
    isPermanent: true,
    isConsumable: false,
    effects: {
      'swipeEqualsTab': true,
      'permanent': true,
    },
  );

  // Multiball - Tier 1
  static const multiballT1 = PowerUp(
    id: 'multiball',
    name: 'Hard Ball',
    description: 'Spawns a hard ball (10 pts) that splits into 4 fast balls (20 pts each)',
    icon: Icons.sports_baseball,
    cost: 80, // Tier 1 cost
    duration: Duration(seconds: 8),
    type: PowerUpType.multiball,
    color: Color(0xFFFF6B35),
    tier: 1,
    maxTier: 3,
    effects: {
      'spawnHardBall': true,
      'initialScore': 10,
      'splitScore': 20,
      'splitCount': 4,
      'ballLifetime': 6.0,
    },
  );

  // Legacy power-ups for backward compatibility
  static const neonBomb = PowerUp(
    id: 'neon_bomb',
    name: 'Neon Bomb',
    description: 'Clears all targets currently on screen',
    icon: Icons.flash_on,
    cost: GameConstants.neonBombCost,
    duration: Duration(milliseconds: 500),
    type: PowerUpType.neonBomb,
    color: Color(0xFFFF00FF),
    effects: {
      'clearAll': true,
      'explosionRadius': 200.0,
    },
  );

  static const magnetTouch = PowerUp(
    id: 'magnet_touch',
    name: 'Magnet Touch',
    description: 'Increases tap hitbox for 10 seconds',
    icon: Icons.touch_app,
    cost: GameConstants.magnetTouchCost,
    duration: Duration(seconds: 10),
    type: PowerUpType.magnetTouch,
    color: Color(0xFFFFD700),
    effects: {
      'hitboxMultiplier': 2.0,
      'magnetRange': 50.0,
    },
  );

  // Legacy aliases for backward compatibility
  static const slowMotion = slowMotionT1;
  static const shield = immuneT1;
  static const timeBoost = timeBoostT1;

  /// Get all available power-ups (tiered system)
  static List<PowerUp> get allPowerUps => [
        timeBoostT1,
        slowMotionT1,
        immuneT1,
        swipeMode,
        multiballT1,
        // Legacy power-ups
        neonBomb,
        magnetTouch,
      ];

  /// Get tiered power-ups only
  static List<PowerUp> get tieredPowerUps => [
        timeBoostT1,
        slowMotionT1,
        immuneT1,
        swipeMode,
      ];

  /// Create a power-up at specific tier
  static PowerUp? createPowerUpAtTier(String powerUpId, int tier) {
    PowerUp basePowerUp;

    switch (powerUpId) {
      case 'time_boost':
        basePowerUp = timeBoostT1;
        break;
      case 'slow_motion':
        basePowerUp = slowMotionT1;
        break;
      case 'immune':
        basePowerUp = immuneT1;
        break;
      case 'swipe_mode':
        return swipeMode; // Single tier only
      default:
        return null;
    }

    if (tier < 1 || tier > basePowerUp.maxTier) {
      return null;
    }

    if (tier == 1) return basePowerUp;

    // Create higher tier version
    final cost = GameCalculations.getPowerUpCost(powerUpId, tier);
    final effects = GameCalculations.getPowerUpEffects(powerUpId, tier);

    if (cost == 0 || effects == null) return null;

    return basePowerUp.copyWith(
      tier: tier,
      cost: cost,
      effects: effects,
      name: '${basePowerUp.name} (Tier $tier)',
      description: _getTierDescription(powerUpId, tier),
    );
  }

  /// Get tier-specific description
  static String _getTierDescription(String powerUpId, int tier) {
    switch (powerUpId) {
      case 'time_boost':
        final timeAdd = [5, 8, 12][tier - 1];
        return 'Adds $timeAdd seconds to the level timer';
      case 'slow_motion':
        final duration = [5, 7, 10][tier - 1];
        return 'Slows down all targets for $duration seconds';
      case 'immune':
        final duration = [5, 8, 10][tier - 1];
        return 'Immune to penalties for $duration seconds';
      default:
        return 'Enhanced power-up effect';
    }
  }

  /// Get power-up by ID
  static PowerUp? getPowerUpById(String id) {
    try {
      return allPowerUps.firstWhere((powerUp) => powerUp.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get power-ups by type
  static List<PowerUp> getPowerUpsByType(PowerUpType type) {
    return allPowerUps.where((powerUp) => powerUp.type == type).toList();
  }
}

/// Represents an owned power-up instance
class OwnedPowerUp {
  final String powerUpId;
  final int quantity;
  final DateTime acquiredAt;
  final bool isEquipped;
  final int tier; // New: tier of the owned power-up

  const OwnedPowerUp({
    required this.powerUpId,
    required this.quantity,
    required this.acquiredAt,
    this.isEquipped = false,
    this.tier = 1,
  });

  /// Creates a copy of this owned power-up with modified properties
  OwnedPowerUp copyWith({
    String? powerUpId,
    int? quantity,
    DateTime? acquiredAt,
    bool? isEquipped,
    int? tier,
  }) {
    return OwnedPowerUp(
      powerUpId: powerUpId ?? this.powerUpId,
      quantity: quantity ?? this.quantity,
      acquiredAt: acquiredAt ?? this.acquiredAt,
      isEquipped: isEquipped ?? this.isEquipped,
      tier: tier ?? this.tier,
    );
  }

  /// Converts owned power-up to JSON
  Map<String, dynamic> toJson() {
    return {
      'powerUpId': powerUpId,
      'quantity': quantity,
      'acquiredAt': acquiredAt.toIso8601String(),
      'isEquipped': isEquipped,
      'tier': tier,
    };
  }

  /// Creates owned power-up from JSON
  factory OwnedPowerUp.fromJson(Map<String, dynamic> json) {
    return OwnedPowerUp(
      powerUpId: json['powerUpId'],
      quantity: json['quantity'],
      acquiredAt: DateTime.parse(json['acquiredAt']),
      isEquipped: json['isEquipped'] ?? false,
      tier: json['tier'] ?? 1,
    );
  }

  /// Get the actual power-up data at the owned tier
  PowerUp? get powerUp => PowerUps.createPowerUpAtTier(powerUpId, tier);

  /// Check if this power-up can be upgraded
  bool get canUpgrade {
    final basePowerUp = PowerUps.createPowerUpAtTier(powerUpId, 1);
    return basePowerUp != null && tier < basePowerUp.maxTier && !basePowerUp.isPermanent;
  }

  /// Get upgrade cost for next tier
  int get upgradeCost {
    if (!canUpgrade) return 0;
    return GameCalculations.getPowerUpCost(powerUpId, tier + 1);
  }
}
