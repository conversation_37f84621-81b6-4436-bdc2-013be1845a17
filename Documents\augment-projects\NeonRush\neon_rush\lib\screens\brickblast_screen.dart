import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flame/game.dart';

import '../providers/brickblast_provider.dart';
import '../providers/game_provider.dart';
import '../games/brickblast_game.dart';
import '../ui/neon_button.dart';
import '../ui/neon_text.dart';
import '../ui/neon_container.dart';
import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../core/sound_manager.dart';
import '../models/brickblast_game_state.dart';

/// Main screen for the BrickBlast game
class BrickBlastScreen extends StatefulWidget {
  const BrickBlastScreen({super.key});

  @override
  State<BrickBlastScreen> createState() => _BrickBlastScreenState();
}

class _BrickBlastScreenState extends State<BrickBlastScreen> {
  late BrickBlastGame game;
  late BrickBlastProvider brickBlastProvider;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGame();
    });
  }

  /// Initialize the game and provider
  Future<void> _initializeGame() async {
    try {
      debugPrint('BrickBlastScreen: Starting initialization...');
      brickBlastProvider = context.read<BrickBlastProvider>();
      final gameProvider = context.read<GameProvider>();
      debugPrint('BrickBlastScreen: Provider obtained, isLoading: ${brickBlastProvider.isLoading}');

      // Set GameProvider reference for token synchronization
      brickBlastProvider.setGameProvider(gameProvider);

      // Initialize provider if not already done
      if (brickBlastProvider.isLoading) {
        debugPrint('BrickBlastScreen: Initializing provider...');
        await brickBlastProvider.initialize();
        debugPrint('BrickBlastScreen: Provider initialization complete');
      }

      // Create and initialize the Flame game
      debugPrint('BrickBlastScreen: Creating Flame game...');
      game = BrickBlastGame();
      game.initializeWithProvider(brickBlastProvider);
      debugPrint('BrickBlastScreen: Flame game initialized');

      // Set up provider callbacks
      brickBlastProvider.onTokensEarned = _showTokensEarned;
      brickBlastProvider.onGameOver = _showGameOverDialog;
      brickBlastProvider.onRoundComplete = _showRoundCompleteDialog;

      if (mounted) {
        debugPrint('BrickBlastScreen: Setting initialized to true');
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing BrickBlast: $e');
      if (mounted) {
        setState(() {
          _isInitialized = true; // Set to true to show error state
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: CircularProgressIndicator(
            color: NeonColors.primaryAccent,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Padding(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + core_constants.GameConstants.topSystemPadding,
          bottom: MediaQuery.of(context).padding.bottom + core_constants.GameConstants.bottomSystemNavPadding,
          left: core_constants.GameConstants.sideSystemPadding,
          right: core_constants.GameConstants.sideSystemPadding,
        ),
        child: Consumer<BrickBlastProvider>(
          builder: (context, provider, child) {
            return Stack(
              children: [
                // Main game area
                Positioned.fill(
                  child: GameWidget<BrickBlastGame>.controlled(
                    gameFactory: () => game,
                  ),
                ),
              
              // Top UI overlay
              _buildTopOverlay(provider),
              
              // Bottom UI overlay
              _buildBottomOverlay(provider),
              
              // Game state overlays
              if (provider.gamePhase == BrickBlastGamePhase.preparing)
                _buildStartGameOverlay(provider),
              
              if (provider.isPaused)
                _buildPauseOverlay(provider),
            ],
          );
        },
        ),
      ),
    );
  }

  /// Build top UI overlay with game bar (similar to main menu)
  Widget _buildTopOverlay(BrickBlastProvider provider) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 10,
      right: 10,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: NeonColors.surface.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: NeonColors.primaryAccent.withValues(alpha: 0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: NeonColors.primaryAccent.withValues(alpha: 0.3),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Round
            _buildStatItem('Round', '${provider.currentRound}', NeonColors.neonGreen),

            // Score
            _buildStatItem('Score', '${provider.currentScore}', NeonColors.primaryAccent),

            // Tokens
            _buildStatItem('Tokens', '${provider.tokensEarned}', NeonColors.neonYellow),

            // Ball HP
            if (provider.balls.isNotEmpty)
              _buildStatItem('Ball HP', '${provider.balls.first.hp}/${provider.balls.first.maxHp}',
                provider.balls.first.hpPercentage > 0.6 ? NeonColors.neonGreen :
                provider.balls.first.hpPercentage > 0.3 ? NeonColors.neonYellow :
                NeonColors.neonRed),

            // Ball Stock
            _buildStatItem('Balls', '${provider.gameState.ballStock}',
              provider.gameState.ballStock > 2 ? NeonColors.neonGreen :
              provider.gameState.ballStock > 0 ? NeonColors.neonYellow :
              NeonColors.neonRed),

            // Pause button
            NeonButton(
              text: '⏸',
              onPressed: () => _pauseGame(provider),
              glowColor: NeonColors.primaryAccent,
              width: 40,
              height: 40,
            ),
          ],
        ),
      ),
    );
  }

  /// Build a stat item for the game bar
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        NeonText(
          label,
          fontSize: 10,
          glowColor: NeonColors.textSecondary,
        ),
        NeonText(
          value,
          fontSize: 14,
          fontWeight: FontWeight.bold,
          glowColor: color,
        ),
      ],
    );
  }



  /// Build bottom UI overlay with controls
  Widget _buildBottomOverlay(BrickBlastProvider provider) {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          NeonButton(
            text: '← Exit',
            onPressed: () => _exitGame(),
            glowColor: NeonColors.error,
            width: 100,
            height: 40,
          ),
          
          // Game instructions
          if (provider.gamePhase == BrickBlastGamePhase.aiming)
            const NeonContainer(
              glowColor: NeonColors.primaryAccent,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: NeonText(
                'Tap or drag to aim and launch ball',
                fontSize: 12,
                glowColor: NeonColors.primaryAccent,
              ),
            ),
        ],
      ),
    );
  }

  /// Build start game overlay
  Widget _buildStartGameOverlay(BrickBlastProvider provider) {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: NeonContainer(
          glowColor: NeonColors.primaryAccent,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const NeonText(
                'BrickBlast',
                fontSize: 32,
                fontWeight: FontWeight.bold,
                glowColor: NeonColors.primaryAccent,
              ),
              const SizedBox(height: 16),
              const NeonText(
                'Destroy bricks with your ball!\nBall HP decreases with each brick hit.',
                fontSize: 16,
                textAlign: TextAlign.center,
                glowColor: NeonColors.primaryAccent,
              ),
              const SizedBox(height: 20),
              NeonButton(
                text: 'Start Game',
                onPressed: () => _startGame(provider),
                glowColor: NeonColors.primaryAccent,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build pause overlay
  Widget _buildPauseOverlay(BrickBlastProvider provider) {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: NeonContainer(
          glowColor: NeonColors.primaryAccent,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const NeonText(
                'Game Paused',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                glowColor: NeonColors.neonYellow,
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  NeonButton(
                    text: 'Resume',
                    onPressed: () => _resumeGame(provider),
                    glowColor: NeonColors.primaryAccent,
                  ),
                  const SizedBox(width: 16),
                  NeonButton(
                    text: 'Exit',
                    onPressed: () => _exitGame(),
                    glowColor: NeonColors.error,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Start a new game
  void _startGame(BrickBlastProvider provider) {
    SoundManager().playButtonClick();
    provider.startNewGame();
  }

  /// Pause the game
  void _pauseGame(BrickBlastProvider provider) {
    SoundManager().playButtonClick();
    provider.pauseGame();
  }

  /// Resume the game
  void _resumeGame(BrickBlastProvider provider) {
    SoundManager().playButtonClick();
    provider.resumeGame();
  }

  /// Exit the game
  void _exitGame() {
    SoundManager().playButtonClick();
    Navigator.of(context).pop();
  }

  /// Show tokens earned notification
  void _showTokensEarned(String message, int tokens) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: NeonText(
          message,
          fontSize: 16,
          glowColor: NeonColors.neonYellow,
        ),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show game over dialog
  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const NeonText(
          'Game Over',
          fontSize: 24,
          fontWeight: FontWeight.bold,
          glowColor: NeonColors.neonRed,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText(
              'Final Score: ${brickBlastProvider.currentScore}',
              fontSize: 18,
              glowColor: NeonColors.primaryAccent,
            ),
            const SizedBox(height: 8),
            NeonText(
              'Tokens Earned: ${brickBlastProvider.tokensEarned}',
              fontSize: 18,
              glowColor: NeonColors.neonYellow,
            ),
            const SizedBox(height: 8),
            NeonText(
              'Round Reached: ${brickBlastProvider.currentRound}',
              fontSize: 18,
              glowColor: NeonColors.neonGreen,
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startGame(brickBlastProvider);
            },
            glowColor: NeonColors.primaryAccent,
          ),
          NeonButton(
            text: 'Exit',
            onPressed: () {
              Navigator.of(context).pop();
              _exitGame();
            },
            glowColor: NeonColors.error,
          ),
        ],
      ),
    );
  }

  /// Show round complete dialog
  void _showRoundCompleteDialog(int round) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const NeonText(
          'Round Complete!',
          fontSize: 24,
          fontWeight: FontWeight.bold,
          glowColor: NeonColors.neonGreen,
        ),
        content: NeonText(
          'Round $round completed!\nPreparing next round...',
          fontSize: 16,
          textAlign: TextAlign.center,
          glowColor: NeonColors.primaryAccent,
        ),
        actions: [
          NeonButton(
            text: 'Continue',
            onPressed: () => Navigator.of(context).pop(),
            glowColor: NeonColors.neonGreen,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    game.pauseEngine();
    super.dispose();
  }
}
