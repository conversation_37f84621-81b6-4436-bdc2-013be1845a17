import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../constants.dart';
import '../../providers/game_provider.dart';

/// Neon Snake bonus game screen
class NeonSnakeScreen extends StatefulWidget {
  const NeonSnakeScreen({super.key});

  @override
  State<NeonSnakeScreen> createState() => _NeonSnakeScreenState();
}

class _NeonSnakeScreenState extends State<NeonSnakeScreen> {
  static const int gridSize = 20;
  static const double cellSize = 15.0;
  
  List<Point<int>> snake = [Point(10, 10)];
  Point<int> food = Point(5, 5);
  Direction direction = Direction.right;
  Direction? nextDirection;
  
  Timer? gameTimer;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  int highScore = 0;
  
  final Random random = Random();

  @override
  void initState() {
    super.initState();
    _loadHighScore();
    _generateFood();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    super.dispose();
  }

  void _loadHighScore() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    highScore = gameProvider.getBonusGameHighScore('neon_snake');
  }

  void _startGame() {
    setState(() {
      snake = [Point(10, 10)];
      direction = Direction.right;
      nextDirection = null;
      score = 0;
      isGameRunning = true;
      isGameOver = false;
    });
    
    _generateFood();
    gameTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      _updateGame();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = !isGameRunning;
    });
    
    if (isGameRunning) {
      gameTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
        _updateGame();
      });
    } else {
      gameTimer?.cancel();
    }
  }

  void _updateGame() {
    if (!isGameRunning || isGameOver) return;

    setState(() {
      // Update direction if there's a queued direction change
      if (nextDirection != null) {
        direction = nextDirection!;
        nextDirection = null;
      }

      // Move snake head
      Point<int> head = snake.first;
      Point<int> newHead;
      
      switch (direction) {
        case Direction.up:
          newHead = Point(head.x, head.y - 1);
          break;
        case Direction.down:
          newHead = Point(head.x, head.y + 1);
          break;
        case Direction.left:
          newHead = Point(head.x - 1, head.y);
          break;
        case Direction.right:
          newHead = Point(head.x + 1, head.y);
          break;
      }

      // Check wall collision
      if (newHead.x < 0 || newHead.x >= gridSize || 
          newHead.y < 0 || newHead.y >= gridSize) {
        _gameOver();
        return;
      }

      // Check self collision
      if (snake.contains(newHead)) {
        _gameOver();
        return;
      }

      // Add new head
      snake.insert(0, newHead);

      // Check food collision
      if (newHead == food) {
        score += 10;
        _generateFood();
        HapticFeedback.lightImpact();
      } else {
        // Remove tail if no food eaten
        snake.removeLast();
      }
    });
  }

  void _generateFood() {
    Point<int> newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood));
    
    food = newFood;
  }

  void _gameOver() {
    gameTimer?.cancel();
    setState(() {
      isGameOver = true;
      isGameRunning = false;
    });

    // Update high score
    if (score > highScore) {
      highScore = score;
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setBonusGameHighScore('neon_snake', score);
    }

    HapticFeedback.heavyImpact();
  }

  void _handleKeyPress(Direction newDirection) {
    if (!isGameRunning || isGameOver) return;
    
    // Prevent reversing into self
    if ((direction == Direction.up && newDirection == Direction.down) ||
        (direction == Direction.down && newDirection == Direction.up) ||
        (direction == Direction.left && newDirection == Direction.right) ||
        (direction == Direction.right && newDirection == Direction.left)) {
      return;
    }
    
    nextDirection = newDirection;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Neon Snake',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isGameRunning ? Icons.pause : Icons.play_arrow,
              color: NeonColors.primaryAccent,
            ),
            onPressed: isGameOver ? null : _pauseGame,
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          decoration: const BoxDecoration(
            gradient: NeonColors.backgroundGradient,
          ),
          child: Column(
          children: [
            // Score display
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildScoreCard('Score', score),
                  _buildScoreCard('High Score', highScore),
                  _buildScoreCard('Length', snake.length),
                ],
              ),
            ),
            
            // Game area
            Expanded(
              child: Center(
                child: Container(
                  width: gridSize * cellSize,
                  height: gridSize * cellSize,
                  decoration: BoxDecoration(
                    color: NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Game grid
                      CustomPaint(
                        size: Size(gridSize * cellSize, gridSize * cellSize),
                        painter: SnakeGamePainter(
                          snake: snake,
                          food: food,
                          cellSize: cellSize,
                        ),
                      ),
                      
                      // Game over overlay
                      if (isGameOver)
                        Container(
                          color: Colors.black.withValues(alpha: 0.7),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Game Over',
                                  style: GoogleFonts.orbitron(
                                    color: NeonColors.error,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Final Score: $score',
                                  style: GoogleFonts.orbitron(
                                    color: NeonColors.textPrimary,
                                    fontSize: 18,
                                  ),
                                ),
                                if (score == highScore) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    'New High Score!',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.success,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                                const SizedBox(height: 24),
                                ElevatedButton(
                                  onPressed: _startGame,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: NeonColors.primaryAccent,
                                    foregroundColor: NeonColors.background,
                                  ),
                                  child: Text(
                                    'Play Again',
                                    style: GoogleFonts.orbitron(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Controls
            if (!isGameOver)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (!isGameRunning && snake.length == 1)
                      ElevatedButton(
                        onPressed: _startGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.primaryAccent,
                          foregroundColor: NeonColors.background,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        ),
                        child: Text(
                          'Start Game',
                          style: GoogleFonts.orbitron(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    _buildDirectionControls(),
                  ],
                ),
              ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 12,
            ),
          ),
          Text(
            value.toString(),
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDirectionControls() {
    return Column(
      children: [
        // Up button
        _buildControlButton(Icons.keyboard_arrow_up, () => _handleKeyPress(Direction.up)),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Left button
            _buildControlButton(Icons.keyboard_arrow_left, () => _handleKeyPress(Direction.left)),
            const SizedBox(width: 32),
            // Right button
            _buildControlButton(Icons.keyboard_arrow_right, () => _handleKeyPress(Direction.right)),
          ],
        ),
        const SizedBox(height: 8),
        // Down button
        _buildControlButton(Icons.keyboard_arrow_down, () => _handleKeyPress(Direction.down)),
      ],
    );
  }

  Widget _buildControlButton(IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: NeonColors.surface,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: NeonColors.primaryAccent.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: NeonColors.primaryAccent.withValues(alpha: 0.2),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: NeonColors.primaryAccent,
          size: 32,
        ),
      ),
    );
  }
}

/// Direction enumeration for snake movement
enum Direction { up, down, left, right }

/// Custom painter for the snake game
class SnakeGamePainter extends CustomPainter {
  final List<Point<int>> snake;
  final Point<int> food;
  final double cellSize;

  SnakeGamePainter({
    required this.snake,
    required this.food,
    required this.cellSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final snakePaint = Paint()
      ..color = NeonColors.primaryAccent
      ..style = PaintingStyle.fill;
    
    final snakeHeadPaint = Paint()
      ..color = NeonColors.highlights
      ..style = PaintingStyle.fill;
    
    final foodPaint = Paint()
      ..color = NeonColors.error
      ..style = PaintingStyle.fill;

    // Draw snake
    for (int i = 0; i < snake.length; i++) {
      final segment = snake[i];
      final rect = Rect.fromLTWH(
        segment.x * cellSize + 1,
        segment.y * cellSize + 1,
        cellSize - 2,
        cellSize - 2,
      );
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        i == 0 ? snakeHeadPaint : snakePaint,
      );
    }

    // Draw food
    final foodRect = Rect.fromLTWH(
      food.x * cellSize + 2,
      food.y * cellSize + 2,
      cellSize - 4,
      cellSize - 4,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(foodRect, const Radius.circular(6)),
      foodPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
