import 'dart:async';
import 'package:flutter/material.dart';

import '../models/brickblast_progress.dart';
import '../models/brickblast_game_state.dart';
import '../models/brickblast_ball.dart';
import '../models/brickblast_brick.dart';
import '../models/token_transaction.dart';
import '../services/token_service.dart';
import '../providers/game_provider.dart';

/// Provider for managing BrickBlast game state and logic
class BrickBlastProvider extends ChangeNotifier {
  BrickBlastProgress _progress = BrickBlastProgress();
  BrickBlastGameState _gameState = BrickBlastGameState();
  bool _isLoading = true;
  Timer? _gameTimer;
  GameProvider? _gameProvider;

  // Callbacks for UI effects
  Function(Offset position, Color color)? onParticleEffect;
  Function(String text, Offset position, Color color)? onFloatingText;
  Function(String message, int tokens)? onTokensEarned;
  Function()? onGameOver;
  Function(int round)? onRoundComplete;

  // Getters
  BrickBlastProgress get progress => _progress;
  BrickBlastGameState get gameState => _gameState;
  bool get isLoading => _isLoading;
  bool get isGameActive => _gameState.isGameActive;
  bool get isPaused => _gameState.isPaused;
  int get currentRound => _gameState.round;
  int get currentScore => _gameState.score;
  int get tokensEarned => _gameState.tokensEarned;
  List<BrickBlastBall> get balls => _gameState.balls;
  List<BrickBlastBrick> get bricks => _gameState.bricks;
  BrickBlastGamePhase get gamePhase => _gameState.phase;

  /// Set the GameProvider reference for token synchronization
  void setGameProvider(GameProvider gameProvider) {
    _gameProvider = gameProvider;
  }

  /// Initialize the provider
  Future<void> initialize() async {
    debugPrint('BrickBlast: Starting initialization...');
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('BrickBlast: Loading progress...');
      _progress = await BrickBlastProgress.load();
      debugPrint('BrickBlast: Progress loaded successfully');

      // If there's a saved game state, restore it
      if (_progress.isGameActive) {
        debugPrint('BrickBlast: Restoring game state...');
        _restoreGameState();
      }
    } catch (e) {
      debugPrint('Error loading BrickBlast progress: $e');
      _progress = BrickBlastProgress();
    }

    debugPrint('BrickBlast: Initialization complete');
    _isLoading = false;
    notifyListeners();
  }

  /// Start a new game
  Future<void> startNewGame() async {
    final round = _progress.currentRound;
    final gameArea = const Size(400, 600);
    
    // Generate bricks for the round
    final bricks = BrickBlastGameState.generateBricksForRound(round, gameArea);
    
    // Create initial ball
    final ball = BrickBlastBall(
      id: 'ball_1',
      hp: 30 + (round * 5), // HP scales with round
      maxHp: 30 + (round * 5),
      position: Offset(gameArea.width / 2, gameArea.height - 50),
    );

    _gameState = BrickBlastGameState(
      round: round,
      phase: BrickBlastGamePhase.aiming,
      balls: [ball],
      bricks: bricks,
      isGameActive: true,
      ballsRemaining: 1,
      ballStock: 5, // Start with 5 balls
      gameArea: gameArea,
    );

    // Update progress
    _progress = _progress.copyWith(
      isGameActive: true,
      currentScore: 0,
      ballsRemaining: 1,
    );

    await _saveProgress();
    notifyListeners();
  }

  /// Launch a ball in the specified direction
  void launchBall(Offset direction) {
    if (_gameState.phase != BrickBlastGamePhase.aiming) return;

    final activeBall = _gameState.balls.firstWhere(
      (ball) => ball.isActive && !ball.isLaunched,
      orElse: () => _gameState.balls.first,
    );

    // Normalize direction and set velocity
    final normalizedDirection = direction / direction.distance;
    final velocity = normalizedDirection * 300.0; // Pixels per second

    activeBall.launch(velocity);

    _gameState = _gameState.copyWith(
      phase: BrickBlastGamePhase.launching,
      aimDirection: null,
    );

    // Update progress statistics
    _progress = _progress.updateStatistics(ballsLaunched: 1);

    _startGameLoop();
    notifyListeners();
  }

  /// Set aim direction for the ball
  void setAimDirection(Offset direction) {
    if (_gameState.phase == BrickBlastGamePhase.aiming) {
      _gameState = _gameState.copyWith(aimDirection: direction);
      notifyListeners();
    }
  }

  /// Start the game loop
  void _startGameLoop() {
    _gameTimer?.cancel();
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      _updateGame(0.016); // ~60 FPS
    });
  }

  /// Update game logic
  void _updateGame(double deltaTime) {
    if (!_gameState.isGameActive || _gameState.isPaused) return;

    // Update ball positions
    for (final ball in _gameState.balls) {
      if (ball.isActive && ball.isLaunched) {
        ball.updatePosition(deltaTime);
        _checkBallCollisions(ball);
        _checkBallBounds(ball);
      }
    }

    // Check if current ball is depleted and spawn new one
    _checkBallDepletion();

    // Check for round completion
    if (_gameState.isRoundComplete) {
      _completeRound();
    }

    // Check for game over
    if (_gameState.shouldGameEnd) {
      _endGame();
    }

    notifyListeners();
  }

  /// Check collisions between ball and bricks
  void _checkBallCollisions(BrickBlastBall ball) {
    for (final brick in _gameState.bricks) {
      if (brick.isDestroyed) continue;

      if (brick.containsPoint(ball.position)) {
        _handleBallBrickCollision(ball, brick);
        break; // Ball can only hit one brick at a time
      }
    }
  }

  /// Handle collision between ball and brick
  void _handleBallBrickCollision(BrickBlastBall ball, BrickBlastBrick brick) {
    // Ball takes damage equal to brick's HP
    ball.takeDamage(brick.hp);
    
    // Brick is destroyed
    brick.takeDamage(brick.hp);
    
    // Calculate rewards
    final score = _gameState.calculateBrickScore(brick);
    final tokens = _gameState.calculateBrickTokens(brick);
    
    // Update game state
    _gameState = _gameState.copyWith(
      score: _gameState.score + score,
      tokensEarned: _gameState.tokensEarned + tokens,
      bricksDestroyed: _gameState.bricksDestroyed + 1,
      comboCount: _gameState.comboCount + 1,
    );

    // Update progress
    _progress = _progress.updateStatistics(
      bricksDestroyed: 1,
      tokensEarned: tokens,
      score: _gameState.score,
    );

    // Trigger effects
    onParticleEffect?.call(brick.center, brick.color);
    onFloatingText?.call('+$score', brick.center, const Color(0xFF00FFFF));
    if (tokens > 0) {
      onFloatingText?.call('+$tokens tokens', brick.center + const Offset(0, -20), const Color(0xFFFFD700));
    }

    // Award tokens to TapVerse economy
    _awardTokens(tokens);
  }

  /// Check ball bounds and handle boundary collisions
  void _checkBallBounds(BrickBlastBall ball) {
    final gameArea = _gameState.gameArea;
    
    // Bottom boundary - ball is lost
    if (ball.position.dy > gameArea.height) {
      ball.isActive = false;
    }
    
    // Side boundaries - ball bounces (optional, based on game design)
    if (ball.position.dx < 0 || ball.position.dx > gameArea.width) {
      ball.velocity = Offset(-ball.velocity.dx, ball.velocity.dy);
      ball.position = Offset(
        ball.position.dx.clamp(0, gameArea.width),
        ball.position.dy,
      );
    }
  }

  /// Check if current ball is depleted and spawn new one from stock
  void _checkBallDepletion() {
    final currentBall = _gameState.balls.isNotEmpty ? _gameState.balls.first : null;

    // Check if current ball is depleted (HP <= 0 or inactive)
    if (currentBall != null && (currentBall.isDepleted || !currentBall.isActive)) {
      // Check if we have balls in stock
      if (_gameState.ballStock > 0) {
        _spawnNewBall();
      } else {
        // No more balls - game over
        _endGame();
      }
    }
  }

  /// Spawn a new ball from stock
  void _spawnNewBall() {
    if (_gameState.ballStock <= 0) return;

    final gameArea = _gameState.gameArea;
    final newBall = BrickBlastBall(
      id: 'ball_${DateTime.now().millisecondsSinceEpoch}',
      hp: 30 + (_gameState.round * 5), // Same HP as round progression
      maxHp: 30 + (_gameState.round * 5),
      position: Offset(gameArea.width / 2, gameArea.height - 50),
    );

    _gameState = _gameState.copyWith(
      balls: [newBall],
      ballStock: _gameState.ballStock - 1,
      phase: BrickBlastGamePhase.aiming, // Reset to aiming phase
    );

    notifyListeners();
  }

  /// Complete the current round
  void _completeRound() {
    _gameTimer?.cancel();
    
    final roundScore = _gameState.score;
    
    // Check if all bricks are destroyed (bonus)
    if (!_gameState.hasBricksRemaining) {
      const bonusScore = 100;
      const bonusTokens = 10;
      
      _gameState = _gameState.copyWith(
        score: _gameState.score + bonusScore,
        tokensEarned: _gameState.tokensEarned + bonusTokens,
      );
      
      _progress = _progress.updateStatistics(
        tokensEarned: bonusTokens,
        score: _gameState.score,
      );
      
      onFloatingText?.call('FULL CLEAR BONUS!', 
        Offset(_gameState.gameArea.width / 2, _gameState.gameArea.height / 2),
        const Color(0xFFFFD700));
      
      _awardTokens(bonusTokens);
    }

    // Update progress for round completion
    _progress = _progress.updateStatistics(
      roundScore: roundScore,
    );

    // Move to next round
    final nextRound = _gameState.round + 1;
    _progress = _progress.copyWith(currentRound: nextRound);
    
    onRoundComplete?.call(_gameState.round);
    
    // Prepare next round
    _prepareNextRound();
  }

  /// Prepare the next round
  void _prepareNextRound() {
    final nextRound = _progress.currentRound;
    final gameArea = _gameState.gameArea;
    
    // Generate new bricks
    final newBricks = BrickBlastGameState.generateBricksForRound(nextRound, gameArea);
    
    // Create new ball with increased HP
    final newBall = BrickBlastBall(
      id: 'ball_$nextRound',
      hp: 30 + (nextRound * 5),
      maxHp: 30 + (nextRound * 5),
      position: Offset(gameArea.width / 2, gameArea.height - 50),
    );

    _gameState = _gameState.copyWith(
      round: nextRound,
      phase: BrickBlastGamePhase.aiming,
      balls: [newBall],
      bricks: newBricks,
      ballsRemaining: 1,
      ballStock: 5, // Reset ball stock for new round
      bricksDestroyed: 0,
      comboCount: 0,
    );

    notifyListeners();
  }

  /// End the game
  void _endGame() {
    _gameTimer?.cancel();
    
    _gameState = _gameState.copyWith(
      phase: BrickBlastGamePhase.gameOver,
      isGameActive: false,
    );

    _progress = _progress.copyWith(
      isGameActive: false,
      currentRound: 1, // Reset to round 1 for next game
    ).updateStatistics(gamesPlayed: 1);

    onGameOver?.call();
    _saveProgress();
    notifyListeners();
  }

  /// Award tokens to the TapVerse economy
  Future<void> _awardTokens(int amount) async {
    if (amount <= 0) return;

    try {
      if (_gameProvider != null) {
        await _gameProvider!.earnTokens(
          amount,
          TokenTransactionType.levelCompletion,
          'BrickBlast reward',
          metadata: {
            'game': 'BrickBlast',
            'round': _gameState.round,
            'score': _gameState.score,
          },
        );
      } else {
        // Fallback to TokenService if GameProvider not available
        await TokenService.earnTokens(
          amount: amount,
          type: TokenTransactionType.levelCompletion,
          description: 'BrickBlast reward',
          metadata: {
            'game': 'BrickBlast',
            'round': _gameState.round,
            'score': _gameState.score,
          },
        );
      }

      onTokensEarned?.call('Earned $amount tokens!', amount);
    } catch (e) {
      debugPrint('Error awarding tokens: $e');
    }
  }

  /// Pause the game
  void pauseGame() {
    if (_gameState.isGameActive) {
      _gameTimer?.cancel();
      _gameState = _gameState.copyWith(isPaused: true);
      notifyListeners();
    }
  }

  /// Resume the game
  void resumeGame() {
    if (_gameState.isGameActive && _gameState.isPaused) {
      _gameState = _gameState.copyWith(isPaused: false);
      if (_gameState.phase == BrickBlastGamePhase.launching) {
        _startGameLoop();
      }
      notifyListeners();
    }
  }

  /// Restore game state from progress
  void _restoreGameState() {
    // Implementation would restore the saved game state
    // For now, just start a new game
    startNewGame();
  }

  /// Save progress to storage
  Future<void> _saveProgress() async {
    try {
      await _progress.save();
    } catch (e) {
      debugPrint('Error saving BrickBlast progress: $e');
    }
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    super.dispose();
  }
}
