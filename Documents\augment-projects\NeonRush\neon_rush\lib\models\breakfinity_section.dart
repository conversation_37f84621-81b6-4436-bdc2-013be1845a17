import 'package:flutter/material.dart';
import 'dart:math';
import 'breakfinity_reward.dart';
import 'breakfinity_layer_theme.dart';

/// Represents a tappable section in the Breakfinity structure
class BreakfinitySection {
  final String id;
  final int layer;
  final int sectionIndex;
  final Offset position;
  final Size size;
  final Color baseColor;
  final int maxHealth;
  final int currentHealth;
  final SectionType type;
  final Map<String, dynamic> rewards;
  final bool isRevealed;
  final bool isDestroyed;
  final DateTime? lastTappedAt;
  final double crackProgress;
  final BreakfinityReward? discoveredReward;
  final double glowIntensity;
  final List<Offset> crackLines;
  final bool hasParticleEffect;

  const BreakfinitySection({
    required this.id,
    required this.layer,
    required this.sectionIndex,
    required this.position,
    required this.size,
    required this.baseColor,
    required this.maxHealth,
    required this.currentHealth,
    required this.type,
    this.rewards = const {},
    this.isRevealed = false,
    this.isDestroyed = false,
    this.lastTappedAt,
    this.crackProgress = 0.0,
    this.discoveredReward,
    this.glowIntensity = 1.0,
    this.crackLines = const [],
    this.hasParticleEffect = false,
  });

  /// Create a copy with updated properties
  BreakfinitySection copyWith({
    String? id,
    int? layer,
    int? sectionIndex,
    Offset? position,
    Size? size,
    Color? baseColor,
    int? maxHealth,
    int? currentHealth,
    SectionType? type,
    Map<String, dynamic>? rewards,
    bool? isRevealed,
    bool? isDestroyed,
    DateTime? lastTappedAt,
    double? crackProgress,
    BreakfinityReward? discoveredReward,
    double? glowIntensity,
    List<Offset>? crackLines,
    bool? hasParticleEffect,
  }) {
    return BreakfinitySection(
      id: id ?? this.id,
      layer: layer ?? this.layer,
      sectionIndex: sectionIndex ?? this.sectionIndex,
      position: position ?? this.position,
      size: size ?? this.size,
      baseColor: baseColor ?? this.baseColor,
      maxHealth: maxHealth ?? this.maxHealth,
      currentHealth: currentHealth ?? this.currentHealth,
      type: type ?? this.type,
      rewards: rewards ?? this.rewards,
      isRevealed: isRevealed ?? this.isRevealed,
      isDestroyed: isDestroyed ?? this.isDestroyed,
      lastTappedAt: lastTappedAt ?? this.lastTappedAt,
      crackProgress: crackProgress ?? this.crackProgress,
      discoveredReward: discoveredReward ?? this.discoveredReward,
      glowIntensity: glowIntensity ?? this.glowIntensity,
      crackLines: crackLines ?? this.crackLines,
      hasParticleEffect: hasParticleEffect ?? this.hasParticleEffect,
    );
  }

  /// Calculate damage dealt to this section
  int calculateDamage(int baseDamage, Map<String, dynamic> powerUps) {
    double multiplier = 1.0;
    
    // Apply power-up multipliers
    if (powerUps.containsKey('damage_multiplier')) {
      multiplier *= powerUps['damage_multiplier'] as double;
    }
    
    // Type-specific bonuses
    switch (type) {
      case SectionType.weak:
        multiplier *= 1.5;
        break;
      case SectionType.reinforced:
        multiplier *= 0.7;
        break;
      case SectionType.crystal:
        multiplier *= 2.0;
        break;
      default:
        break;
    }
    
    return (baseDamage * multiplier).round();
  }

  /// Get the layer theme for this section
  BreakfinityLayerTheme get layerTheme => BreakfinityLayerThemes.getThemeForLayer(layer);

  /// Get themed color for this section
  Color get themedColor => layerTheme.getSectionColor(sectionIndex, type.name);

  /// Get glow color based on theme and current state
  Color get glowColor {
    final baseGlow = layerTheme.getGlowColor();
    if (isDestroyed) {
      return baseGlow.withValues(alpha: 0.0);
    }
    final healthRatio = currentHealth / maxHealth;
    final intensity = glowIntensity * (1.0 - healthRatio * 0.5);
    return baseGlow.withValues(alpha: (baseGlow.a * intensity));
  }

  /// Get particle color for breaking effects
  Color get particleColor => layerTheme.getParticleColor();

  /// Check if section should have special visual effects
  bool get hasSpecialEffects => type == SectionType.crystal || type == SectionType.mystery;

  /// Generate crack lines based on damage
  List<Offset> generateCrackLines() {
    if (crackProgress <= 0.0) return [];

    final random = Random(id.hashCode); // Consistent cracks for same section
    final lines = <Offset>[];
    final center = Offset(size.width / 2, size.height / 2);

    final numCracks = (crackProgress * 6).round().clamp(1, 6);
    for (int i = 0; i < numCracks; i++) {
      final angle = (i / numCracks) * 2 * pi + random.nextDouble() * 0.5;
      final length = (size.width / 3) * crackProgress * (0.5 + random.nextDouble() * 0.5);

      lines.add(center);
      lines.add(center + Offset(cos(angle) * length, sin(angle) * length));
    }

    return lines;
  }

  /// Get the visual crack pattern based on damage
  List<Offset> getCrackPattern() {
    if (crackProgress <= 0) return [];
    
    final random = Random(id.hashCode);
    final cracks = <Offset>[];
    final crackCount = (crackProgress * 8).round();
    
    for (int i = 0; i < crackCount; i++) {
      cracks.add(Offset(
        random.nextDouble() * size.width,
        random.nextDouble() * size.height,
      ));
    }
    
    return cracks;
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'layer': layer,
      'sectionIndex': sectionIndex,
      'position': {'dx': position.dx, 'dy': position.dy},
      'size': {'width': size.width, 'height': size.height},
      'baseColor': baseColor.toARGB32(),
      'maxHealth': maxHealth,
      'currentHealth': currentHealth,
      'type': type.name,
      'rewards': rewards,
      'isRevealed': isRevealed,
      'isDestroyed': isDestroyed,
      'lastTappedAt': lastTappedAt?.toIso8601String(),
      'crackProgress': crackProgress,
    };
  }

  /// Create from JSON
  factory BreakfinitySection.fromJson(Map<String, dynamic> json) {
    return BreakfinitySection(
      id: json['id'],
      layer: json['layer'],
      sectionIndex: json['sectionIndex'],
      position: Offset(json['position']['dx'], json['position']['dy']),
      size: Size(json['size']['width'], json['size']['height']),
      baseColor: Color(json['baseColor']),
      maxHealth: json['maxHealth'],
      currentHealth: json['currentHealth'],
      type: SectionType.values.firstWhere((t) => t.name == json['type']),
      rewards: Map<String, dynamic>.from(json['rewards'] ?? {}),
      isRevealed: json['isRevealed'] ?? false,
      isDestroyed: json['isDestroyed'] ?? false,
      lastTappedAt: json['lastTappedAt'] != null 
          ? DateTime.parse(json['lastTappedAt']) 
          : null,
      crackProgress: json['crackProgress']?.toDouble() ?? 0.0,
    );
  }
}

/// Types of sections with different properties
enum SectionType {
  normal,     // Standard section
  weak,       // Takes less damage to break
  reinforced, // Takes more damage to break
  crystal,    // Gives bonus rewards when broken
  mystery,    // Contains random rewards
  explosive,  // Damages nearby sections when broken
  regenerating, // Slowly heals over time
}

/// Reward types that can be found in sections
enum RewardType {
  tokens,
  powerUps,
  themes,
  multipliers,
  autoTappers,
  specialItems,
}

/// Factory for creating sections with appropriate rewards
class SectionFactory {
  static final Random _random = Random();

  /// Generate a section for a specific layer
  static BreakfinitySection generateSection({
    required int layer,
    required int sectionIndex,
    required Offset position,
    required Size size,
  }) {
    final type = _generateSectionType(layer);
    final health = _calculateHealth(layer, type);
    final theme = BreakfinityLayerThemes.getThemeForLayer(layer);
    final color = theme.getSectionColor(sectionIndex, type.name);
    final rewards = _generateRewards(layer, type);
    final glowIntensity = theme.glowIntensity * _getTypeGlowMultiplier(type);

    return BreakfinitySection(
      id: 'layer_${layer}_section_$sectionIndex',
      layer: layer,
      sectionIndex: sectionIndex,
      position: position,
      size: size,
      baseColor: color,
      maxHealth: health,
      currentHealth: health,
      type: type,
      rewards: rewards,
      glowIntensity: glowIntensity,
    );
  }

  static SectionType _generateSectionType(int layer) {
    // Higher layers have more special section types
    final specialChance = min(0.3, layer / 100.0);
    
    if (_random.nextDouble() < specialChance) {
      return SectionType.values[_random.nextInt(SectionType.values.length)];
    }
    
    return SectionType.normal;
  }

  static double _getTypeGlowMultiplier(SectionType type) {
    switch (type) {
      case SectionType.crystal:
        return 1.5;
      case SectionType.mystery:
        return 2.0;
      case SectionType.weak:
        return 0.8;
      case SectionType.reinforced:
        return 1.2;
      default:
        return 1.0;
    }
  }

  static int _calculateHealth(int layer, SectionType type) {
    int baseHealth = 5 + layer; // Reduced from 10 + (layer * 2) to make sections easier to break

    switch (type) {
      case SectionType.weak:
        return (baseHealth * 0.5).round();
      case SectionType.reinforced:
        return (baseHealth * 2.0).round();
      case SectionType.crystal:
        return (baseHealth * 1.5).round();
      default:
        return baseHealth;
    }
  }



  static Map<String, dynamic> _generateRewards(int layer, SectionType type) {
    final rewards = <String, dynamic>{};
    final random = _random.nextDouble();

    // New reward system based on rarity
    if (random < 0.01) {
      // Very Rare (1%): App theme unlock or 3 keys
      if (_random.nextBool()) {
        rewards['theme_unlock'] = _getRandomThemeUnlock();
      } else {
        rewards['keys'] = 3;
      }
    } else if (random < 0.06) {
      // Rare (5%): 20 tokens
      rewards['tokens'] = 20;
    } else if (random < 0.16) {
      // Uncommon (10%): 10 tokens
      rewards['tokens'] = 10;
    } else if (random < 0.36) {
      // Common (20%): 1 token
      rewards['tokens'] = 1;
    }
    // 64% chance of no reward

    // Special section type bonuses
    switch (type) {
      case SectionType.crystal:
        // Crystal sections always have at least uncommon rewards
        if (!rewards.containsKey('tokens') || (rewards['tokens'] as int) < 10) {
          rewards['tokens'] = 10;
        }
        break;
      case SectionType.mystery:
        // Mystery sections have higher chance for rare rewards
        if (_random.nextDouble() < 0.3) {
          rewards['tokens'] = 20;
        }
        break;
      default:
        break;
    }

    return rewards;
  }

  /// Get a random theme unlock for very rare rewards
  static String _getRandomThemeUnlock() {
    final availableThemes = [
      'cyber_blue',
      'neon_pink',
      'electric_green',
      'fire_orange',
      'ice_blue',
      'cosmic_purple',
      'rainbow_infinity'
    ];
    return availableThemes[_random.nextInt(availableThemes.length)];
  }
}
