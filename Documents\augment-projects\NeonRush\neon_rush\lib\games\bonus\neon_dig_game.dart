import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';

import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// Neon Dig - Tap to break through neon-colored earth layers
class NeonDigGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const NeonDigGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<NeonDigGame> createState() => _NeonDigGameState();
}

class _NeonDigGameState extends State<NeonDigGame>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  
  final List<DigBlock> _blocks = [];
  final List<ParticleEffect> _particles = [];
  int _score = 0;
  int _depth = 0;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _level = 1;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _startNewGame();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _blocks.clear();
      _particles.clear();
      _score = 0;
      _depth = 0;
      _gameActive = true;
      _gameStarted = true;
      _level = 1;
    });

    _generateBlocks();
  }

  void _generateBlocks() {
    final screenWidth = MediaQuery.of(context).size.width;
    final blocksPerRow = 6;
    final blockWidth = screenWidth / blocksPerRow;
    final blockHeight = 60.0;
    
    // Generate 50 rows of blocks
    for (int row = 0; row < 50; row++) {
      for (int col = 0; col < blocksPerRow; col++) {
        final block = _createBlock(row, col, blockWidth, blockHeight);
        _blocks.add(block);
      }
    }
  }

  DigBlock _createBlock(int row, int col, double width, double height) {
    final random = Random();
    final depth = row + 1;
    
    // Determine block type based on depth
    BlockType type;
    int hitPoints;
    Color color;
    
    if (depth % 10 == 0) {
      // Boss block every 10 rows
      type = BlockType.boss;
      hitPoints = 5;
      color = const Color(0xFFFF0000);
    } else if (random.nextDouble() < 0.1) {
      // 10% chance for treasure
      type = BlockType.treasure;
      hitPoints = 1;
      color = const Color(0xFFFFD700);
    } else if (random.nextDouble() < 0.05) {
      // 5% chance for trap
      type = BlockType.trap;
      hitPoints = 1;
      color = const Color(0xFF800080);
    } else {
      // Regular block
      type = BlockType.normal;
      hitPoints = min(3, 1 + (depth ~/ 5)); // Increase HP with depth
      color = _getDepthColor(depth);
    }
    
    return DigBlock(
      x: col * width,
      y: row * height,
      width: width,
      height: height,
      type: type,
      hitPoints: hitPoints,
      maxHitPoints: hitPoints,
      color: color,
      row: row,
      col: col,
    );
  }

  Color _getDepthColor(int depth) {
    final colors = [
      const Color(0xFF00FFFF), // Cyan
      const Color(0xFF00FF00), // Green
      const Color(0xFFFFFF00), // Yellow
      const Color(0xFFFF6B35), // Orange
      const Color(0xFFFF00FF), // Magenta
    ];
    
    return colors[(depth ~/ 5) % colors.length];
  }

  void _tapBlock(DigBlock block) async {
    if (!_gameActive || block.hitPoints <= 0) return;

    await SoundManager().playSfx(SoundType.buttonClick);
    
    final blockIndex = _blocks.indexWhere((b) => 
        b.row == block.row && b.col == block.col);
    
    if (blockIndex == -1) return;
    
    setState(() {
      final updatedBlock = block.copyWith(
        hitPoints: block.hitPoints - 1,
      );
      _blocks[blockIndex] = updatedBlock;
      
      // Add particle effect
      _particles.add(ParticleEffect(
        x: block.x + block.width / 2,
        y: block.y + block.height / 2,
        color: block.color,
        createdAt: DateTime.now(),
      ));
    });

    if (block.hitPoints - 1 <= 0) {
      await _handleBlockDestroyed(block);
    } else {
      await NeonHaptics.targetHit();
    }
    
    // Remove old particles
    _cleanupParticles();
  }

  Future<void> _handleBlockDestroyed(DigBlock block) async {
    switch (block.type) {
      case BlockType.normal:
        await NeonHaptics.targetHit();
        setState(() {
          _score += 10;
          _depth = max(_depth, block.row + 1);
        });
        break;
        
      case BlockType.treasure:
        await NeonHaptics.comboAchieved();
        setState(() {
          _score += 50;
          _depth = max(_depth, block.row + 1);
        });
        break;
        
      case BlockType.trap:
        await NeonHaptics.gameOver();
        setState(() {
          _score = max(0, _score - 25);
        });
        break;
        
      case BlockType.boss:
        await NeonHaptics.levelComplete();
        setState(() {
          _score += 100;
          _depth = max(_depth, block.row + 1);
          _level++;
        });
        break;
    }
    
    // Auto-scroll down when blocks are destroyed
    if (block.type != BlockType.trap) {
      _autoScroll();
    }
    
    // Check for game end
    if (_depth >= 50) {
      _endGame();
    }
  }

  void _autoScroll() {
    final targetOffset = _depth * 60.0 - 200;
    if (targetOffset > _scrollController.offset) {
      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _cleanupParticles() {
    setState(() {
      _particles.removeWhere((particle) =>
          DateTime.now().difference(particle.createdAt).inMilliseconds > 1000);
    });
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.levelComplete();

    if (!mounted) return;

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 10); // 1 token per 10 points

    // Call the completion callback instead of showing dialog directly
    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, _score ~/ 10);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            Positioned.fill(
              top: 80,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: SizedBox(
                  height: _blocks.length * 60.0 / 6, // 6 blocks per row
                  child: Stack(
                    children: [
                      // Blocks
                      ..._blocks.map((block) => _buildBlock(block)),
                      // Particles
                      ..._particles.map((particle) => _buildParticle(particle)),
                    ],
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Depth: $_depth',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFF00FF00),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'TAP BLOCKS TO DIG',
                        glowColor: Colors.white,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFD700),
                              border: Border.all(color: const Color(0xFFFFD700)),
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            'Treasure  ',
                            glowColor: const Color(0xFFFFD700),
                            fontSize: 12,
                          ),
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: const Color(0xFF800080),
                              border: Border.all(color: const Color(0xFF800080)),
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            'Trap  ',
                            glowColor: const Color(0xFF800080),
                            fontSize: 12,
                          ),
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFF0000),
                              border: Border.all(color: const Color(0xFFFF0000)),
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            'Boss',
                            glowColor: const Color(0xFFFF0000),
                            fontSize: 12,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlock(DigBlock block) {
    if (block.hitPoints <= 0) {
      return const SizedBox.shrink();
    }
    
    return Positioned(
      left: block.x,
      top: block.y,
      child: GestureDetector(
        onTap: () => _tapBlock(block),
        child: Container(
          width: block.width,
          height: block.height,
          decoration: BoxDecoration(
            color: block.color.withValues(alpha: 0.3 + (block.hitPoints / block.maxHitPoints) * 0.5),
            border: Border.all(
              color: block.color,
              width: block.type == BlockType.boss ? 3 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: block.color.withValues(alpha: 0.5),
                blurRadius: block.type == BlockType.boss ? 8 : 4,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (block.type == BlockType.treasure)
                  const Icon(Icons.star, color: Colors.white, size: 20)
                else if (block.type == BlockType.trap)
                  const Icon(Icons.warning, color: Colors.white, size: 20)
                else if (block.type == BlockType.boss)
                  const Icon(Icons.shield, color: Colors.white, size: 20),
                if (block.hitPoints > 1)
                  NeonText.body(
                    '${block.hitPoints}',
                    glowColor: Colors.white,
                    fontSize: 12,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildParticle(ParticleEffect particle) {
    final age = DateTime.now().difference(particle.createdAt).inMilliseconds;
    final alpha = (1.0 - (age / 1000.0)).clamp(0.0, 1.0);
    
    return Positioned(
      left: particle.x - 10,
      top: particle.y - 10,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: particle.color.withValues(alpha: alpha),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: particle.color.withValues(alpha: alpha * 0.5),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }
}

/// Block types in the dig game
enum BlockType {
  normal,
  treasure,
  trap,
  boss,
}

/// Represents a diggable block
class DigBlock {
  final double x;
  final double y;
  final double width;
  final double height;
  final BlockType type;
  final int hitPoints;
  final int maxHitPoints;
  final Color color;
  final int row;
  final int col;

  const DigBlock({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.type,
    required this.hitPoints,
    required this.maxHitPoints,
    required this.color,
    required this.row,
    required this.col,
  });

  DigBlock copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    BlockType? type,
    int? hitPoints,
    int? maxHitPoints,
    Color? color,
    int? row,
    int? col,
  }) {
    return DigBlock(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      type: type ?? this.type,
      hitPoints: hitPoints ?? this.hitPoints,
      maxHitPoints: maxHitPoints ?? this.maxHitPoints,
      color: color ?? this.color,
      row: row ?? this.row,
      col: col ?? this.col,
    );
  }
}

/// Represents a particle effect
class ParticleEffect {
  final double x;
  final double y;
  final Color color;
  final DateTime createdAt;

  const ParticleEffect({
    required this.x,
    required this.y,
    required this.color,
    required this.createdAt,
  });
}
