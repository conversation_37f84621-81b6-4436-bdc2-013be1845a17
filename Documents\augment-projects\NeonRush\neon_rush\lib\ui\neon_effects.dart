import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../core/constants.dart';

/// Utility class for creating neon glow effects
class NeonEffects {
  /// Creates a neon glow box shadow
  static List<BoxShadow> createGlow({
    required Color color,
    double radius = GameConstants.glowRadius,
    double spread = GameConstants.glowSpread,
    double intensity = 1.0,
  }) {
    return [
      BoxShadow(
        color: color.withValues(alpha: 0.3 * intensity),
        blurRadius: radius * 0.7,
        spreadRadius: spread * 0.5,
      ),
      BoxShadow(
        color: color.withValues(alpha: 0.15 * intensity),
        blurRadius: radius,
        spreadRadius: spread,
      ),
      BoxShadow(
        color: color.withValues(alpha: 0.08 * intensity),
        blurRadius: radius * 1.5,
        spreadRadius: spread * 1.2,
      ),
    ];
  }

  /// Creates a neon gradient
  static LinearGradient createNeonGradient({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: _generateStops(colors.length),
    );
  }

  /// Creates a radial neon gradient
  static RadialGradient createRadialNeonGradient({
    required List<Color> colors,
    AlignmentGeometry center = Alignment.center,
    double radius = 1.0,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: _generateStops(colors.length),
    );
  }

  /// Creates a pulsing animation
  static Widget createPulseAnimation({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1500),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .scale(
          duration: duration,
          begin: Offset(minScale, minScale),
          end: Offset(maxScale, maxScale),
          curve: Curves.easeInOut,
        );
  }

  /// Creates a shimmer effect
  static Widget createShimmerEffect({
    required Widget child,
    Duration duration = const Duration(milliseconds: 2000),
    Color? shimmerColor,
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat())
        .shimmer(
          duration: duration,
          color: shimmerColor ?? Colors.white.withValues(alpha: 0.3),
        );
  }

  /// Creates a glow animation that pulses
  static Widget createGlowPulse({
    required Widget child,
    required Color glowColor,
    Duration duration = const Duration(milliseconds: 1200),
    double minIntensity = 0.5,
    double maxIntensity = 1.0,
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .custom(
          duration: duration,
          builder: (context, value, child) {
            final intensity = minIntensity + (maxIntensity - minIntensity) * value;
            return Container(
              decoration: BoxDecoration(
                boxShadow: createGlow(
                  color: glowColor,
                  intensity: intensity,
                ),
              ),
              child: child,
            );
          },
        );
  }

  /// Creates a neon border effect
  static BoxDecoration createNeonBorder({
    required Color color,
    double borderWidth = 2.0,
    double borderRadius = 8.0,
    double glowIntensity = 1.0,
  }) {
    return BoxDecoration(
      border: Border.all(
        color: color,
        width: borderWidth,
      ),
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: createGlow(
        color: color,
        intensity: glowIntensity,
      ),
    );
  }

  /// Creates a neon text shadow
  static List<Shadow> createTextGlow({
    required Color color,
    double blurRadius = 10.0,
    double intensity = 1.0,
  }) {
    return [
      Shadow(
        color: color.withValues(alpha: (0.8 * intensity).clamp(0.0, 1.0)),
        blurRadius: blurRadius,
      ),
      Shadow(
        color: color.withValues(alpha: (0.4 * intensity).clamp(0.0, 1.0)),
        blurRadius: blurRadius * 1.5,
      ),
      Shadow(
        color: color.withValues(alpha: (0.2 * intensity).clamp(0.0, 1.0)),
        blurRadius: blurRadius * 2,
      ),
    ];
  }

  /// Creates a particle effect animation
  static Widget createParticleEffect({
    required Widget child,
    int particleCount = 20,
    Duration duration = const Duration(milliseconds: 3000),
    Color particleColor = Colors.white,
  }) {
    return Stack(
      children: [
        child,
        ...List.generate(particleCount, (index) {
          return Positioned.fill(
            child: _ParticleWidget(
              color: particleColor,
              delay: Duration(milliseconds: (index * 150) % duration.inMilliseconds),
              duration: duration,
            ),
          );
        }),
      ],
    );
  }

  /// Helper method to generate gradient stops
  static List<double> _generateStops(int colorCount) {
    if (colorCount <= 1) return [0.0];
    return List.generate(colorCount, (index) => index / (colorCount - 1));
  }
}

/// Widget for individual particles in particle effects
class _ParticleWidget extends StatefulWidget {
  final Color color;
  final Duration delay;
  final Duration duration;

  const _ParticleWidget({
    required this.color,
    required this.delay,
    required this.duration,
  });

  @override
  State<_ParticleWidget> createState() => _ParticleWidgetState();
}

class _ParticleWidgetState extends State<_ParticleWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.repeat();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final progress = _animation.value;
        final opacity = (1.0 - progress).clamp(0.0, 1.0);
        final size = 2.0 + progress * 4.0;
        
        return Positioned(
          left: progress * MediaQuery.of(context).size.width,
          top: progress * MediaQuery.of(context).size.height,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: widget.color.withValues(alpha: opacity),
              shape: BoxShape.circle,
              boxShadow: NeonEffects.createGlow(
                color: widget.color,
                radius: size,
                intensity: opacity,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Extension to add neon effects to any widget
extension NeonWidgetExtensions on Widget {
  /// Adds a neon glow effect to any widget
  Widget withNeonGlow({
    required Color color,
    double intensity = 1.0,
    bool animate = false,
  }) {
    final glowWidget = Container(
      decoration: BoxDecoration(
        boxShadow: NeonEffects.createGlow(
          color: color,
          intensity: intensity,
        ),
      ),
      child: this,
    );

    if (animate) {
      return NeonEffects.createGlowPulse(
        child: glowWidget,
        glowColor: color,
      );
    }

    return glowWidget;
  }

  /// Adds a pulsing animation to any widget
  Widget withPulse({
    Duration duration = const Duration(milliseconds: 1500),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return NeonEffects.createPulseAnimation(
      child: this,
      duration: duration,
      minScale: minScale,
      maxScale: maxScale,
    );
  }

  /// Adds a shimmer effect to any widget
  Widget withShimmer({
    Duration duration = const Duration(milliseconds: 2000),
    Color? shimmerColor,
  }) {
    return NeonEffects.createShimmerEffect(
      child: this,
      duration: duration,
      shimmerColor: shimmerColor,
    );
  }
}
