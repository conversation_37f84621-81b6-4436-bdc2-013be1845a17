import 'dart:ui';
import 'dart:math';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';

/// Particle component for BrickBlast destruction effects
class ParticleComponent extends PositionComponent {
  final Color color;
  final double maxLifetime;
  double lifetime;
  final List<Particle> particles = [];
  final Random _random = Random();

  ParticleComponent({
    required Vector2 position,
    required this.color,
    this.maxLifetime = 2.0,
  }) : lifetime = 0.0,
       super(position: position, size: Vector2(100, 100)) {
    _generateParticles();
  }

  /// Generate particles for the explosion effect
  void _generateParticles() {
    const particleCount = 15;
    
    for (int i = 0; i < particleCount; i++) {
      final angle = _random.nextDouble() * 2 * pi;
      final speed = _random.nextDouble() * 100 + 50;
      final size = _random.nextDouble() * 3 + 1;
      final particleLifetime = _random.nextDouble() * maxLifetime + 0.5;
      
      particles.add(Particle(
        position: Vector2.zero(),
        velocity: Vector2(cos(angle) * speed, sin(angle) * speed),
        size: size,
        color: color,
        lifetime: particleLifetime,
        maxLifetime: particleLifetime,
      ));
    }
  }

  @override
  void render(Canvas canvas) {
    for (final particle in particles) {
      if (particle.isAlive) {
        particle.render(canvas);
      }
    }
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    lifetime += dt;
    
    // Update all particles
    for (final particle in particles) {
      particle.update(dt);
    }
    
    // Remove dead particles
    particles.removeWhere((particle) => !particle.isAlive);
    
    // Remove component when all particles are dead
    if (particles.isEmpty || lifetime > maxLifetime) {
      removeFromParent();
    }
  }
}

/// Individual particle for effects
class Particle {
  Vector2 position;
  Vector2 velocity;
  double size;
  Color color;
  double lifetime;
  double maxLifetime;
  late Paint paint;
  late Paint glowPaint;

  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.color,
    required this.lifetime,
    required this.maxLifetime,
  }) {
    _updatePaints();
  }

  /// Update paint objects
  void _updatePaints() {
    final alpha = (1.0 - lifetime / maxLifetime).clamp(0.0, 1.0);
    
    paint = Paint()
      ..color = color.withValues(alpha: alpha)
      ..style = PaintingStyle.fill;

    glowPaint = Paint()
      ..color = color.withValues(alpha: alpha * 0.5)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);
  }

  /// Check if particle is still alive
  bool get isAlive => lifetime < maxLifetime;

  /// Render the particle
  void render(Canvas canvas) {
    if (!isAlive) return;
    
    // Draw glow
    canvas.drawCircle(
      position.toOffset(),
      size * 1.5,
      glowPaint,
    );
    
    // Draw particle
    canvas.drawCircle(
      position.toOffset(),
      size,
      paint,
    );
  }

  /// Update particle physics
  void update(double dt) {
    if (!isAlive) return;
    
    lifetime += dt;
    
    // Update position
    position += velocity * dt;
    
    // Apply gravity
    velocity.y += 200 * dt; // Gravity acceleration
    
    // Apply air resistance
    velocity *= 0.98;
    
    // Shrink particle over time
    final ageRatio = lifetime / maxLifetime;
    size = size * (1.0 - ageRatio * 0.5);
    
    // Update paints
    _updatePaints();
  }
}

/// Specialized particle component for brick destruction
class BrickDestructionParticles extends ParticleComponent {
  BrickDestructionParticles({
    required super.position,
    required Color brickColor,
  }) : super(
    color: brickColor,
    maxLifetime: 1.5,
  );

  @override
  void _generateParticles() {
    const particleCount = 20;
    
    for (int i = 0; i < particleCount; i++) {
      final angle = _random.nextDouble() * 2 * pi;
      final speed = _random.nextDouble() * 150 + 75;
      final size = _random.nextDouble() * 4 + 2;
      final particleLifetime = _random.nextDouble() * maxLifetime + 0.3;
      
      // Create rectangular particles for brick fragments
      particles.add(BrickFragment(
        position: Vector2.zero(),
        velocity: Vector2(cos(angle) * speed, sin(angle) * speed),
        size: size,
        color: color,
        lifetime: particleLifetime,
        maxLifetime: particleLifetime,
        width: _random.nextDouble() * 6 + 2,
        height: _random.nextDouble() * 4 + 1,
      ));
    }
  }
}

/// Brick fragment particle
class BrickFragment extends Particle {
  final double width;
  final double height;
  double rotation = 0.0;
  double rotationSpeed;

  BrickFragment({
    required super.position,
    required super.velocity,
    required super.size,
    required super.color,
    required super.lifetime,
    required super.maxLifetime,
    required this.width,
    required this.height,
  }) : rotationSpeed = (Random().nextDouble() - 0.5) * 10;

  @override
  void render(Canvas canvas) {
    if (!isAlive) return;
    
    canvas.save();
    canvas.translate(position.x, position.y);
    canvas.rotate(rotation);
    
    // Draw glow
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: Offset.zero, width: width * 1.5, height: height * 1.5),
        const Radius.circular(1),
      ),
      glowPaint,
    );
    
    // Draw fragment
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: Offset.zero, width: width, height: height),
        const Radius.circular(0.5),
      ),
      paint,
    );
    
    canvas.restore();
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Update rotation
    rotation += rotationSpeed * dt;
  }
}
