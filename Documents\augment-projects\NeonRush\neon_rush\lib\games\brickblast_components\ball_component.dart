import 'dart:math';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';

import '../../models/brickblast_ball.dart';

/// Flame component for rendering a BrickBlast ball
class BallComponent extends PositionComponent {
  BrickBlastBall ball;
  late Paint ballPaint;
  late Paint glowPaint;
  late Paint trailPaint;
  
  // Trail effect
  final List<Offset> trailPositions = [];
  final int maxTrailLength = 10;

  BallComponent(this.ball) : super(
    position: Vector2(ball.position.dx, ball.position.dy),
    size: Vector2(ball.radius * 2, ball.radius * 2),
  ) {
    _updatePaints();
  }

  /// Update paint objects based on ball state
  void _updatePaints() {
    // Main ball paint
    ballPaint = Paint()
      ..color = ball.color
      ..style = PaintingStyle.fill;
    
    // Glow effect paint
    glowPaint = Paint()
      ..color = ball.color.withValues(alpha: 0.3 * ball.glowIntensity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);

    // Trail paint
    trailPaint = Paint()
      ..color = ball.color.withValues(alpha: 0.5 * ball.glowIntensity)
      ..style = PaintingStyle.fill;
  }

  /// Update ball data and position
  void updateBall(BrickBlastBall newBall) {
    final oldHp = ball.hp;
    ball = newBall;
    position = Vector2(ball.position.dx - ball.radius, ball.position.dy - ball.radius);
    _updatePaints();

    // Add damage effect if HP decreased
    if (newBall.hp < oldHp) {
      addDamageEffect();
    }

    // Add to trail if ball is moving
    if (ball.isLaunched && ball.isActive) {
      trailPositions.add(ball.position);
      if (trailPositions.length > maxTrailLength) {
        trailPositions.removeAt(0);
      }
    }
  }

  @override
  void render(Canvas canvas) {
    if (!ball.isActive) return;
    
    // Draw trail
    _renderTrail(canvas);
    
    // Draw glow effect
    canvas.drawCircle(
      Offset(ball.radius, ball.radius),
      ball.radius * 1.5,
      glowPaint,
    );
    
    // Draw main ball
    canvas.drawCircle(
      Offset(ball.radius, ball.radius),
      ball.radius,
      ballPaint,
    );
    
    // Draw HP indicator
    _renderHPIndicator(canvas);
    
    // Draw inner glow
    final innerGlowPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3 * ball.glowIntensity)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(ball.radius, ball.radius),
      ball.radius * 0.6,
      innerGlowPaint,
    );
  }

  /// Render trail effect
  void _renderTrail(Canvas canvas) {
    if (trailPositions.length < 2) return;
    
    for (int i = 0; i < trailPositions.length - 1; i++) {
      final alpha = (i / trailPositions.length) * ball.glowIntensity;
      final trailSize = ball.radius * (0.3 + alpha * 0.7);
      
      final trailPaintAlpha = Paint()
        ..color = ball.color.withValues(alpha: alpha * 0.5)
        ..style = PaintingStyle.fill;
      
      // Convert world position to local position
      final localPos = trailPositions[i] - ball.position + Offset(ball.radius, ball.radius);
      
      canvas.drawCircle(
        localPos,
        trailSize,
        trailPaintAlpha,
      );
    }
  }

  /// Render HP indicator around the ball
  void _renderHPIndicator(Canvas canvas) {
    if (ball.hp <= 0) return;
    
    const indicatorRadius = 12.0;
    const strokeWidth = 2.0;
    final hpPercentage = ball.hpPercentage;
    
    // Background circle
    final bgPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    
    canvas.drawCircle(
      Offset(ball.radius, ball.radius),
      indicatorRadius,
      bgPaint,
    );
    
    // HP arc
    final hpPaint = Paint()
      ..color = _getHPColor(hpPercentage)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;
    
    const startAngle = -1.5708; // -90 degrees (top)
    final sweepAngle = 6.2832 * hpPercentage; // Full circle * percentage
    
    canvas.drawArc(
      Rect.fromCircle(
        center: Offset(ball.radius, ball.radius),
        radius: indicatorRadius,
      ),
      startAngle,
      sweepAngle,
      false,
      hpPaint,
    );
  }

  /// Get color based on HP percentage
  Color _getHPColor(double hpPercentage) {
    if (hpPercentage > 0.6) {
      return const Color(0xFF00FF00); // Green
    } else if (hpPercentage > 0.3) {
      return const Color(0xFFFFFF00); // Yellow
    } else {
      return const Color(0xFFFF0000); // Red
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update position from ball
    position = Vector2(ball.position.dx - ball.radius, ball.position.dy - ball.radius);

    // Update paints if ball state changed
    _updatePaints();

    // Add pulsing effect based on HP
    if (ball.hp > 0) {
      final pulseScale = 1.0 + (1.0 - ball.hpPercentage) * 0.1 * (1.0 + 0.5 * sin(dt * 10));
      scale = Vector2.all(pulseScale);
    }
  }



  /// Add visual damage effect using simple animation
  void addDamageEffect() {
    // Simple visual feedback - we'll implement this later with proper Flame effects
    // For now, just update the visual state
    _updatePaints();
  }
}
