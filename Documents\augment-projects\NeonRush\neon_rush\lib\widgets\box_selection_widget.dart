import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'dart:math';

import '../constants.dart';
import '../core/sound_manager.dart';
import '../utils/haptic_feedback.dart';
import '../models/power_up.dart';

/// Reward types for mystery boxes
enum RewardType { tokens, powerUp }

/// Mystery box reward model
class MysteryBoxReward {
  final RewardType type;
  final int? tokens;
  final PowerUp? powerUp;
  final String displayText;
  final IconData icon;
  final Color color;

  const MysteryBoxReward({
    required this.type,
    this.tokens,
    this.powerUp,
    required this.displayText,
    required this.icon,
    required this.color,
  });
}

/// Widget for selecting reward boxes after level completion
class BoxSelectionWidget extends StatefulWidget {
  final Function(MysteryBoxReward reward) onBoxSelected;
  final bool enabled;

  const BoxSelectionWidget({
    super.key,
    required this.onBoxSelected,
    this.enabled = true,
  });

  @override
  State<BoxSelectionWidget> createState() => _BoxSelectionWidgetState();
}

class _BoxSelectionWidgetState extends State<BoxSelectionWidget>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _glowAnimations;
  
  List<MysteryBoxReward> _rewards = [];
  int? _selectedBox;
  bool _revealed = false;

  @override
  void initState() {
    super.initState();
    _generateRewards();
    _initializeAnimations();
  }

  void _generateRewards() {
    final random = Random();
    _rewards = [];

    // Generate 4 random rewards
    for (int i = 0; i < 4; i++) {
      if (random.nextBool()) {
        // Token reward (70% chance)
        final tokenAmount = [5, 10, 15, 25, 50][random.nextInt(5)];
        _rewards.add(MysteryBoxReward(
          type: RewardType.tokens,
          tokens: tokenAmount,
          displayText: '$tokenAmount Tokens',
          icon: Icons.toll,
          color: NeonColors.primaryAccent,
        ));
      } else {
        // Power-up reward (30% chance)
        final powerUps = PowerUps.allPowerUps;
        final powerUp = powerUps[random.nextInt(powerUps.length)];
        _rewards.add(MysteryBoxReward(
          type: RewardType.powerUp,
          powerUp: powerUp,
          displayText: powerUp.name,
          icon: powerUp.icon,
          color: powerUp.color,
        ));
      }
    }

    // Shuffle the rewards
    _rewards.shuffle(random);
  }

  void _initializeAnimations() {
    _controllers = List.generate(4, (index) => 
      AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      )
    );

    _scaleAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      )
    ).toList();

    _glowAnimations = _controllers.map((controller) =>
      Tween<double>(begin: 0.5, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      )
    ).toList();

    // Start entrance animations
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _selectBox(int index) async {
    if (!widget.enabled || _selectedBox != null) return;

    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();

    setState(() {
      _selectedBox = index;
      _revealed = true;
    });

    // Animate selected box
    _controllers[index].repeat(reverse: true);

    // Reveal all boxes after a delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        widget.onBoxSelected(_rewards[index]);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'SELECT YOUR REWARD',
          style: GoogleFonts.orbitron(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: NeonColors.primaryAccent,
            letterSpacing: 2,
          ),
        ),
        const SizedBox(height: 24),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.0,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: 4,
          itemBuilder: (context, index) => _buildBox(index),
        ),
        if (_revealed) ...[
          const SizedBox(height: 24),
          Text(
            'You selected: ${_rewards[_selectedBox!].displayText}!',
            style: GoogleFonts.orbitron(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: NeonColors.success,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBox(int index) {
    final isSelected = _selectedBox == index;
    final shouldReveal = _revealed;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimations[index], _glowAnimations[index]]),
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected ? _scaleAnimations[index].value : 1.0,
          child: GestureDetector(
            onTap: () => _selectBox(index),
            child: Container(
              decoration: BoxDecoration(
                color: shouldReveal 
                    ? (isSelected ? NeonColors.success.withValues(alpha: 0.2) : NeonColors.surface)
                    : NeonColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: shouldReveal && isSelected 
                      ? NeonColors.success 
                      : NeonColors.primaryAccent,
                  width: isSelected ? 3 : 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (shouldReveal && isSelected 
                        ? NeonColors.success 
                        : NeonColors.primaryAccent).withValues(alpha: _glowAnimations[index].value * 0.5),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Center(
                child: shouldReveal
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.toll,
                            color: isSelected ? NeonColors.success : NeonColors.primaryAccent,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Icon(
                            _rewards[index].icon,
                            color: _rewards[index].color,
                            size: 32,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _rewards[index].displayText,
                            style: GoogleFonts.orbitron(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: isSelected ? NeonColors.success : NeonColors.primaryAccent,
                            ),
                          ),
                        ],
                      )
                    : Icon(
                        Icons.card_giftcard,
                        color: NeonColors.primaryAccent,
                        size: 48,
                      ),
              ),
            ),
          ),
        );
      },
    );
  }
}
