import 'package:flutter/material.dart';

import 'package:google_fonts/google_fonts.dart';
import 'dart:math';

import '../models/breakfinity_reward.dart';
import '../constants.dart';

/// Widget for displaying discovered rewards with animations
class BreakfinityRewardDisplay extends StatefulWidget {
  final BreakfinityReward reward;
  final VoidCallback? onDismiss;
  final Duration displayDuration;

  const BreakfinityRewardDisplay({
    super.key,
    required this.reward,
    this.onDismiss,
    this.displayDuration = const Duration(seconds: 3),
  });

  @override
  State<BreakfinityRewardDisplay> createState() => _BreakfinityRewardDisplayState();
}

class _BreakfinityRewardDisplayState extends State<BreakfinityRewardDisplay>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _glowController;
  late AnimationController _particleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _slideController.forward();
    _glowController.repeat(reverse: true);
    _particleController.forward();

    // Auto dismiss after duration
    Future.delayed(widget.displayDuration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _glowController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  void _dismiss() {
    _slideController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: SlideTransition(
          position: _slideAnimation,
          child: Container(
            margin: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 300),
            child: Stack(
              children: [
                // Particle effects background
                _buildParticleEffects(),
                
                // Main reward card
                _buildRewardCard(),
                
                // Glow effect overlay
                _buildGlowOverlay(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildParticleEffects() {
    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(300, 200),
          painter: _RewardParticlePainter(
            progress: _particleController.value,
            color: widget.reward.rarityColor,
          ),
        );
      },
    );
  }

  Widget _buildRewardCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: NeonColors.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: widget.reward.rarityColor,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: widget.reward.rarityColor.withValues(alpha: 0.5),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Rarity indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: widget.reward.rarityColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              widget.reward.rarityName.toUpperCase(),
              style: GoogleFonts.orbitron(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Reward icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: widget.reward.color.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: widget.reward.color,
                width: 2,
              ),
            ),
            child: Icon(
              widget.reward.icon,
              size: 40,
              color: widget.reward.color,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Reward name
          Text(
            widget.reward.name,
            style: GoogleFonts.orbitron(
              color: NeonColors.textPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Reward description
          Text(
            widget.reward.description,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Tap to dismiss hint
          Text(
            'Tap to dismiss',
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlowOverlay() {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: widget.reward.rarityColor.withValues(alpha: _glowAnimation.value * 0.3),
                blurRadius: 30 * _glowAnimation.value,
                spreadRadius: 10 * _glowAnimation.value,
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Custom painter for reward particle effects
class _RewardParticlePainter extends CustomPainter {
  final double progress;
  final Color color;
  final Random _random = Random(42); // Fixed seed for consistent animation

  _RewardParticlePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw sparkle particles
    for (int i = 0; i < 20; i++) {
      final angle = (i / 20.0) * 2 * pi;
      final distance = 50 + (progress * 100);
      final particleSize = 3.0 * (1.0 - progress);
      
      if (particleSize > 0) {
        final position = center + Offset(
          cos(angle) * distance,
          sin(angle) * distance,
        );
        
        canvas.drawCircle(position, particleSize, paint);
      }
    }
    
    // Draw floating particles
    for (int i = 0; i < 15; i++) {
      final x = _random.nextDouble() * size.width;
      final y = _random.nextDouble() * size.height;
      final particleProgress = (progress + i * 0.1) % 1.0;
      final alpha = (1.0 - particleProgress) * 0.5;
      final particleSize = 2.0 + _random.nextDouble() * 3.0;
      
      if (alpha > 0) {
        final particlePaint = Paint()
          ..color = color.withValues(alpha: alpha)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(
          Offset(x, y - particleProgress * 50),
          particleSize,
          particlePaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Overlay widget for showing rewards on top of the game
class BreakfinityRewardOverlay extends StatefulWidget {
  final Widget child;

  const BreakfinityRewardOverlay({
    super.key,
    required this.child,
  });

  @override
  State<BreakfinityRewardOverlay> createState() => _BreakfinityRewardOverlayState();
}

class _BreakfinityRewardOverlayState extends State<BreakfinityRewardOverlay> {
  final List<BreakfinityReward> _pendingRewards = [];

  void showReward(BreakfinityReward reward) {
    setState(() {
      _pendingRewards.add(reward);
    });
  }

  void _dismissReward(BreakfinityReward reward) {
    setState(() {
      _pendingRewards.remove(reward);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // Reward displays
        ..._pendingRewards.map((reward) => Positioned.fill(
          child: GestureDetector(
            onTap: () => _dismissReward(reward),
            child: BreakfinityRewardDisplay(
              reward: reward,
              onDismiss: () => _dismissReward(reward),
            ),
          ),
        )),
      ],
    );
  }
}
