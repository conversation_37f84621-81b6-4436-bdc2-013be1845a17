import 'dart:ui';
import 'brickblast_ball.dart';
import 'brickblast_brick.dart';

/// Represents the current state of a BrickBlast game session
enum BrickBlastGamePhase {
  preparing,  // Setting up the round
  aiming,     // Player is aiming the ball
  launching,  // Ball is in motion
  evaluating, // Checking round completion
  gameOver,   // Game has ended
}

/// Power-up types available in BrickBlast
enum BrickBlastPowerUp {
  ballSplitter,  // Split ball into two paths
  piercingShot,  // No HP cost for one brick
  hpBoost,       // Add +10 to next ball
  multiShot,     // Launch multiple balls
  explosive,     // Ball explodes on impact
}

/// Represents the complete game state for a BrickBlast session
class BrickBlastGameState {
  final int round;
  final int score;
  final int tokensEarned;
  final BrickBlastGamePhase phase;
  final List<BrickBlastBall> balls;
  final List<BrickBlastBrick> bricks;
  final List<BrickBlastPowerUp> activePowerUps;
  final Offset? aimDirection;
  final bool isGameActive;
  final bool isPaused;
  final int ballsRemaining; // Deprecated - use ballStock instead
  final int ballStock; // Number of balls available to spawn
  final int bricksDestroyed;
  final int comboCount;
  final double gameSpeed;
  
  // Round configuration
  final int maxBallHp;
  final int ballsPerRound;
  final double brickSpacing;
  final Size gameArea;
  
  // Visual effects
  final List<ParticleEffect> particles;
  final List<FloatingText> floatingTexts;
  
  BrickBlastGameState({
    this.round = 1,
    this.score = 0,
    this.tokensEarned = 0,
    this.phase = BrickBlastGamePhase.preparing,
    List<BrickBlastBall>? balls,
    List<BrickBlastBrick>? bricks,
    List<BrickBlastPowerUp>? activePowerUps,
    this.aimDirection,
    this.isGameActive = false,
    this.isPaused = false,
    this.ballsRemaining = 1,
    this.ballStock = 5, // Start with 5 balls
    this.bricksDestroyed = 0,
    this.comboCount = 0,
    this.gameSpeed = 1.0,
    int? maxBallHp,
    this.ballsPerRound = 1,
    this.brickSpacing = 45.0,
    this.gameArea = const Size(400, 600),
    List<ParticleEffect>? particles,
    List<FloatingText>? floatingTexts,
  }) : balls = balls ?? [],
       bricks = bricks ?? [],
       activePowerUps = activePowerUps ?? [],
       maxBallHp = maxBallHp ?? (30 + round * 5),
       particles = particles ?? [],
       floatingTexts = floatingTexts ?? [];

  /// Create a copy with modified properties
  BrickBlastGameState copyWith({
    int? round,
    int? score,
    int? tokensEarned,
    BrickBlastGamePhase? phase,
    List<BrickBlastBall>? balls,
    List<BrickBlastBrick>? bricks,
    List<BrickBlastPowerUp>? activePowerUps,
    Offset? aimDirection,
    bool? isGameActive,
    bool? isPaused,
    int? ballsRemaining,
    int? ballStock,
    int? bricksDestroyed,
    int? comboCount,
    double? gameSpeed,
    int? maxBallHp,
    int? ballsPerRound,
    double? brickSpacing,
    Size? gameArea,
    List<ParticleEffect>? particles,
    List<FloatingText>? floatingTexts,
  }) {
    return BrickBlastGameState(
      round: round ?? this.round,
      score: score ?? this.score,
      tokensEarned: tokensEarned ?? this.tokensEarned,
      phase: phase ?? this.phase,
      balls: balls ?? List.from(this.balls),
      bricks: bricks ?? List.from(this.bricks),
      activePowerUps: activePowerUps ?? List.from(this.activePowerUps),
      aimDirection: aimDirection ?? this.aimDirection,
      isGameActive: isGameActive ?? this.isGameActive,
      isPaused: isPaused ?? this.isPaused,
      ballsRemaining: ballsRemaining ?? this.ballsRemaining,
      ballStock: ballStock ?? this.ballStock,
      bricksDestroyed: bricksDestroyed ?? this.bricksDestroyed,
      comboCount: comboCount ?? this.comboCount,
      gameSpeed: gameSpeed ?? this.gameSpeed,
      maxBallHp: maxBallHp ?? this.maxBallHp,
      ballsPerRound: ballsPerRound ?? this.ballsPerRound,
      brickSpacing: brickSpacing ?? this.brickSpacing,
      gameArea: gameArea ?? this.gameArea,
      particles: particles ?? List.from(this.particles),
      floatingTexts: floatingTexts ?? List.from(this.floatingTexts),
    );
  }

  /// Check if all balls are inactive
  bool get allBallsInactive => balls.every((ball) => !ball.isActive);

  /// Check if any bricks remain
  bool get hasBricksRemaining => bricks.any((brick) => !brick.isDestroyed);

  /// Check if round is complete
  bool get isRoundComplete => allBallsInactive && phase == BrickBlastGamePhase.launching;

  /// Check if game should end (bricks reached bottom)
  bool get shouldGameEnd {
    const bottomThreshold = 550.0; // Near bottom of game area
    return bricks.any((brick) => !brick.isDestroyed && brick.position.dy > bottomThreshold);
  }

  /// Get active balls
  List<BrickBlastBall> get activeBalls => balls.where((ball) => ball.isActive).toList();

  /// Get remaining bricks
  List<BrickBlastBrick> get remainingBricks => bricks.where((brick) => !brick.isDestroyed).toList();

  /// Calculate score for destroying a brick
  int calculateBrickScore(BrickBlastBrick brick) {
    int baseScore = brick.tokenReward * 10; // Base score is 10x token reward
    
    // Combo multiplier
    if (comboCount > 1) {
      baseScore = (baseScore * (1 + comboCount * 0.1)).round();
    }
    
    // Round multiplier
    baseScore = (baseScore * (1 + round * 0.05)).round();
    
    return baseScore;
  }

  /// Calculate tokens for destroying a brick
  int calculateBrickTokens(BrickBlastBrick brick) {
    int baseTokens = brick.tokenReward;
    
    // Combo bonus
    if (comboCount >= 3) {
      baseTokens += 1;
    }
    if (comboCount >= 5) {
      baseTokens += 1;
    }
    
    return baseTokens;
  }

  /// Generate bricks for the current round
  static List<BrickBlastBrick> generateBricksForRound(int round, Size gameArea) {
    final bricks = <BrickBlastBrick>[];
    final brickWidth = 40.0;
    final brickHeight = 20.0;
    final spacing = 5.0;
    final rows = 3 + (round ~/ 5); // More rows as rounds progress
    final cols = (gameArea.width / (brickWidth + spacing)).floor();

    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        // Skip some bricks randomly for variety
        if (row > 0 && (col + row) % 7 == 0) continue;

        final x = col * (brickWidth + spacing) + spacing;
        final y = 120.0 + row * (brickHeight + spacing); // Moved down to avoid UI overlay

        // Determine brick type based on position and round
        BrickType type = BrickType.normal;
        if (row == 0 && round > 3) {
          type = BrickType.shield; // Top row gets shield bricks
        } else if ((col + row) % 5 == 0 && round > 2) {
          type = BrickType.strong;
        } else if ((col + row) % 8 == 0 && round > 5) {
          type = BrickType.reinforced;
        } else if ((col + row) % 12 == 0 && round > 8) {
          type = BrickType.explosive;
        }

        bricks.add(BrickBlastBrick.forRound(
          id: 'brick_${row}_$col',
          round: round,
          type: type,
          position: Offset(x, y),
          size: Size(brickWidth, brickHeight),
        ));
      }
    }

    return bricks;
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'round': round,
      'score': score,
      'tokensEarned': tokensEarned,
      'phase': phase.index,
      'balls': balls.map((ball) => ball.toJson()).toList(),
      'bricks': bricks.map((brick) => brick.toJson()).toList(),
      'activePowerUps': activePowerUps.map((powerUp) => powerUp.index).toList(),
      'aimDirection': aimDirection != null ? {'x': aimDirection!.dx, 'y': aimDirection!.dy} : null,
      'isGameActive': isGameActive,
      'isPaused': isPaused,
      'ballsRemaining': ballsRemaining,
      'ballStock': ballStock,
      'bricksDestroyed': bricksDestroyed,
      'comboCount': comboCount,
      'gameSpeed': gameSpeed,
      'maxBallHp': maxBallHp,
      'ballsPerRound': ballsPerRound,
      'brickSpacing': brickSpacing,
      'gameArea': {'width': gameArea.width, 'height': gameArea.height},
    };
  }

  /// Create from JSON
  factory BrickBlastGameState.fromJson(Map<String, dynamic> json) {
    return BrickBlastGameState(
      round: json['round'] ?? 1,
      score: json['score'] ?? 0,
      tokensEarned: json['tokensEarned'] ?? 0,
      phase: BrickBlastGamePhase.values[json['phase'] ?? 0],
      balls: (json['balls'] as List?)?.map((ballJson) => BrickBlastBall.fromJson(ballJson)).toList() ?? [],
      bricks: (json['bricks'] as List?)?.map((brickJson) => BrickBlastBrick.fromJson(brickJson)).toList() ?? [],
      activePowerUps: (json['activePowerUps'] as List?)?.map((index) => BrickBlastPowerUp.values[index]).toList() ?? [],
      aimDirection: json['aimDirection'] != null ? Offset(json['aimDirection']['x'], json['aimDirection']['y']) : null,
      isGameActive: json['isGameActive'] ?? false,
      isPaused: json['isPaused'] ?? false,
      ballsRemaining: json['ballsRemaining'] ?? 1,
      ballStock: json['ballStock'] ?? 5,
      bricksDestroyed: json['bricksDestroyed'] ?? 0,
      comboCount: json['comboCount'] ?? 0,
      gameSpeed: json['gameSpeed'] ?? 1.0,
      maxBallHp: json['maxBallHp'],
      ballsPerRound: json['ballsPerRound'] ?? 1,
      brickSpacing: json['brickSpacing'] ?? 45.0,
      gameArea: json['gameArea'] != null ? Size(json['gameArea']['width'], json['gameArea']['height']) : const Size(400, 600),
    );
  }
}

/// Represents a particle effect in the game
class ParticleEffect {
  final Offset position;
  final Color color;
  final double size;
  final Offset velocity;
  final double lifetime;
  final double age;

  ParticleEffect({
    required this.position,
    required this.color,
    required this.size,
    required this.velocity,
    required this.lifetime,
    this.age = 0.0,
  });
}

/// Represents floating text in the game
class FloatingText {
  final String text;
  final Offset position;
  final Color color;
  final double fontSize;
  final Offset velocity;
  final double lifetime;
  final double age;

  FloatingText({
    required this.text,
    required this.position,
    required this.color,
    required this.fontSize,
    required this.velocity,
    required this.lifetime,
    this.age = 0.0,
  });
}
