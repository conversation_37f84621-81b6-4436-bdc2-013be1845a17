import 'package:flutter/material.dart';
import 'dart:math';

import '../models/breakfinity_section.dart';
import '../models/breakfinity_layer_theme.dart';

/// Enhanced renderer for Breakfinity sections with theme support
class BreakfinitySectionRenderer extends StatefulWidget {
  final BreakfinitySection section;
  final VoidCallback? onTap;
  final bool showCracks;
  final bool showGlow;
  final double animationProgress;

  const BreakfinitySectionRenderer({
    super.key,
    required this.section,
    this.onTap,
    this.showCracks = true,
    this.showGlow = true,
    this.animationProgress = 0.0,
  });

  @override
  State<BreakfinitySectionRenderer> createState() => _BreakfinitySectionRendererState();
}

class _BreakfinitySectionRendererState extends State<BreakfinitySectionRenderer>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start animations for special sections
    if (widget.section.hasSpecialEffects) {
      _pulseController.repeat(reverse: true);
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Always wrap in GestureDetector to maintain consistent interaction
    return GestureDetector(
      onTap: widget.section.isDestroyed ? null : widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _glowAnimation]),
        builder: (context, child) {
          return CustomPaint(
            size: widget.section.size,
            painter: widget.section.isDestroyed
                ? _DestroyedSectionPainter(
                    section: widget.section,
                    animationProgress: widget.animationProgress,
                  )
                : _SectionPainter(
                    section: widget.section,
                    pulseValue: _pulseAnimation.value,
                    glowValue: _glowAnimation.value,
                    showCracks: widget.showCracks,
                    showGlow: widget.showGlow,
                    animationProgress: widget.animationProgress,
                  ),
          );
        },
      ),
    );
  }
}

/// Custom painter for rendering intact sections
class _SectionPainter extends CustomPainter {
  final BreakfinitySection section;
  final double pulseValue;
  final double glowValue;
  final bool showCracks;
  final bool showGlow;
  final double animationProgress;

  _SectionPainter({
    required this.section,
    required this.pulseValue,
    required this.glowValue,
    required this.showCracks,
    required this.showGlow,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw glow effect
    if (showGlow && !section.isDestroyed) {
      _drawGlowEffect(canvas, rect, center);
    }
    
    // Draw main section
    _drawMainSection(canvas, rect);
    
    // Skip type-specific effects for uniform appearance
    // All sections now show only their themed colors
    
    // Draw cracks
    if (showCracks && section.crackProgress > 0) {
      _drawCracks(canvas, size);
    }
    
    // Draw health indicator
    _drawHealthIndicator(canvas, rect);
  }

  void _drawGlowEffect(Canvas canvas, Rect rect, Offset center) {
    final glowColor = section.glowColor;
    final glowIntensity = glowValue * 0.8;

    // Outer glow
    final outerGlowPaint = Paint()
      ..color = glowColor.withValues(alpha: 0.2 * glowIntensity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0);

    final outerRect = RRect.fromRectAndRadius(
      rect.inflate(4.0),
      Radius.circular(rect.width * 0.15),
    );
    canvas.drawRRect(outerRect, outerGlowPaint);

    // Inner glow
    final innerGlowPaint = Paint()
      ..color = glowColor.withValues(alpha: 0.4 * glowIntensity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    final innerRect = RRect.fromRectAndRadius(
      rect.inflate(1.0),
      Radius.circular(rect.width * 0.12),
    );
    canvas.drawRRect(innerRect, innerGlowPaint);
  }

  void _drawMainSection(Canvas canvas, Rect rect) {
    // Apply pulse effect for special sections
    if (section.hasSpecialEffects) {
      final scale = pulseValue;
      canvas.save();
      canvas.scale(scale, scale);
      canvas.translate(rect.width * (1 - scale) / 2, rect.height * (1 - scale) / 2);
    }

    final borderRadius = min(rect.width, rect.height) * 0.12;
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // Draw main section with neon gradient
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        section.themedColor.withValues(alpha: 0.9),
        section.themedColor.withValues(alpha: 0.7),
        section.themedColor.withValues(alpha: 0.9),
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(rrect, paint);

    // Draw neon border with glow
    final borderPaint = Paint()
      ..color = section.layerTheme.accentColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);
    canvas.drawRRect(rrect, borderPaint);

    // Draw inner bright border
    final innerBorderPaint = Paint()
      ..color = section.layerTheme.accentColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    canvas.drawRRect(rrect, innerBorderPaint);

    if (section.hasSpecialEffects) {
      canvas.restore();
    }
  }









  void _drawCracks(Canvas canvas, Size size) {
    final crackLines = section.generateCrackLines();
    if (crackLines.isEmpty) return;
    
    final paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    for (int i = 0; i < crackLines.length; i += 2) {
      if (i + 1 < crackLines.length) {
        canvas.drawLine(crackLines[i], crackLines[i + 1], paint);
      }
    }
  }

  void _drawHealthIndicator(Canvas canvas, Rect rect) {
    if (section.currentHealth >= section.maxHealth) return;

    final healthRatio = section.currentHealth / section.maxHealth;
    final barWidth = rect.width * 0.8;
    final barHeight = 3.0;
    final barRect = Rect.fromLTWH(
      (rect.width - barWidth) / 2,
      rect.height - barHeight - 3,
      barWidth,
      barHeight,
    );

    // Background with neon border
    final bgPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(barRect, const Radius.circular(1.5)),
      bgPaint,
    );

    // Health bar with neon colors
    final healthRect = Rect.fromLTWH(
      barRect.left,
      barRect.top,
      barRect.width * healthRatio,
      barRect.height,
    );

    final healthColor = Color.lerp(
      const Color(0xFFFF0040), // Neon red
      const Color(0xFF00FF40), // Neon green
      healthRatio,
    )!;

    // Health bar glow
    final glowPaint = Paint()
      ..color = healthColor.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);

    canvas.drawRRect(
      RRect.fromRectAndRadius(healthRect.inflate(0.5), const Radius.circular(1.5)),
      glowPaint,
    );

    // Health bar main
    final healthPaint = Paint()
      ..color = healthColor
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(healthRect, const Radius.circular(1.5)),
      healthPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for destroyed sections showing next layer preview
class _DestroyedSectionPainter extends CustomPainter {
  final BreakfinitySection section;
  final double animationProgress;

  _DestroyedSectionPainter({
    required this.section,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Show next layer preview with neon styling
    final nextLayerTheme = BreakfinityLayerThemes.getThemeForLayer(section.layer + 1);
    final previewColor = nextLayerTheme.primaryColor.withValues(alpha: 0.2);

    final borderRadius = min(rect.width, rect.height) * 0.12;
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // Draw preview background with subtle glow
    final glowPaint = Paint()
      ..color = previewColor.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);

    canvas.drawRRect(rrect.inflate(1.0), glowPaint);

    final paint = Paint()
      ..color = previewColor
      ..style = PaintingStyle.fill;

    canvas.drawRRect(rrect, paint);

    // Draw preview border with neon glow
    final borderPaint = Paint()
      ..color = nextLayerTheme.accentColor.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 0.5);

    canvas.drawRRect(rrect, borderPaint);

    // Draw inner bright border
    final innerBorderPaint = Paint()
      ..color = nextLayerTheme.accentColor.withValues(alpha: 0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    canvas.drawRRect(rrect, innerBorderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
