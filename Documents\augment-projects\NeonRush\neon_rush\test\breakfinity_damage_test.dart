import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:neon_rush/models/breakfinity_progress.dart';
import 'package:neon_rush/models/breakfinity_section.dart';
import 'dart:developer' as developer;

void main() {
  group('Breakfinity Damage Tests', () {
    test('BreakfinityProgress should have correct default base damage', () {
      final progress = BreakfinityProgress(lastPlayedAt: DateTime.now());

      expect(progress.baseDamage, equals(1));
      expect(progress.getEffectiveDamage(), equals(1));
    });

    test('Section health should be reasonable for early layers', () {
      // Test layer 1 sections
      final normalSection = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 0,
        position: const Offset(0, 0),
        size: const Size(50, 50),
      );

      final weakSection = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 1,
        position: const Offset(50, 0),
        size: const Size(50, 50),
      );
      
      // Layer 1 base health should be 10 + (1 * 2) = 12
      // Section types are randomly generated, so just check they have reasonable health
      expect(normalSection.maxHealth, greaterThan(0));
      expect(weakSection.maxHealth, greaterThan(0));
      expect(normalSection.maxHealth, lessThanOrEqualTo(24)); // Max for reinforced: 12 * 2
      expect(weakSection.maxHealth, lessThanOrEqualTo(24));
      
      developer.log('Normal section health: ${normalSection.maxHealth}', name: 'BreakfinityTest');
      developer.log('Weak section health: ${weakSection.maxHealth}', name: 'BreakfinityTest');
    });

    test('Damage calculation should break sections in reasonable time', () {
      final progress = BreakfinityProgress(lastPlayedAt: DateTime.now());
      final section = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 0,
        position: const Offset(0, 0),
        size: const Size(50, 50),
      );
      
      final baseDamage = progress.getEffectiveDamage();
      final sectionDamage = section.calculateDamage(baseDamage, progress.powerUps);
      final tapsToBreak = (section.maxHealth / sectionDamage).ceil();
      
      developer.log('Base damage: $baseDamage', name: 'BreakfinityTest');
      developer.log('Section damage: $sectionDamage', name: 'BreakfinityTest');
      developer.log('Section health: ${section.maxHealth}', name: 'BreakfinityTest');
      developer.log('Taps to break: $tapsToBreak', name: 'BreakfinityTest');
      
      // Should take reasonable number of taps to break any section on layer 1
      expect(tapsToBreak, lessThanOrEqualTo(25)); // With base damage 1, expect reasonable tap count
    });

    test('Damage multipliers work correctly', () {
      final progress = BreakfinityProgress(
        lastPlayedAt: DateTime.now(),
        baseDamage: 1,
        tapMultiplier: 2.0,
      );

      expect(progress.getEffectiveDamage(), equals(2));
    });

    test('Power-up effects work correctly', () {
      final progress = BreakfinityProgress(
        lastPlayedAt: DateTime.now(),
        baseDamage: 1,
        powerUps: {'mega_tap': 1},
      );

      // Mega tap should multiply by 5
      expect(progress.getEffectiveDamage(), equals(5));
    });
  });
}
