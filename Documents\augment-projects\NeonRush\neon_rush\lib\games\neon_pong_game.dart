import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../core/sound_manager.dart';
import '../models/neon_theme.dart';
import '../ui/neon_text.dart';
import '../ui/neon_button.dart';
import '../ui/neon_container.dart';


/// Neon Pong bonus game
class NeonPongGame extends StatefulWidget {
  const NeonPongGame({super.key});

  @override
  State<NeonPongGame> createState() => _NeonPongGameState();
}

class _NeonPongGameState extends State<NeonPongGame>
    with TickerProviderStateMixin {
  // Game state
  bool _isPlaying = false;
  bool _isPaused = false;
  bool _gameOver = false;
  int _playerScore = 0;
  int _aiScore = 0;
  Timer? _gameTimer;
  
  // Game objects
  late Offset _ballPosition;
  late Offset _ballVelocity;
  late double _playerPaddleY;
  late double _aiPaddleY;
  
  // Game constants
  static const double _ballSize = 12.0;
  static const double _paddleWidth = 12.0;
  static const double _paddleHeight = 80.0;
  static const double _ballSpeed = 4.0;
  static const double _paddleSpeed = 6.0;
  static const int _winningScore = 7;
  
  // Screen dimensions
  late Size _gameArea;
  
  // Animation controllers
  late AnimationController _ballGlowController;
  late AnimationController _scoreController;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _resetGame();
  }
  
  void _initializeAnimations() {
    _ballGlowController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
    
    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
  }
  
  @override
  void dispose() {
    _gameTimer?.cancel();
    _ballGlowController.dispose();
    _scoreController.dispose();
    super.dispose();
  }
  
  void _resetGame() {
    setState(() {
      _isPlaying = false;
      _isPaused = false;
      _gameOver = false;
      _playerScore = 0;
      _aiScore = 0;
    });
    _resetBall();
  }
  
  void _resetBall() {
    _ballPosition = const Offset(0.5, 0.5); // Center of screen (normalized)
    final random = Random();
    final angle = (random.nextDouble() - 0.5) * pi / 3; // ±30 degrees
    final direction = random.nextBool() ? 1 : -1;
    _ballVelocity = Offset(
      direction * _ballSpeed * cos(angle),
      _ballSpeed * sin(angle),
    );
    _playerPaddleY = 0.5;
    _aiPaddleY = 0.5;
  }
  
  void _startGame() {
    if (_gameArea.isEmpty) return;
    
    setState(() {
      _isPlaying = true;
      _isPaused = false;
    });
    
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), _updateGame);
    SoundManager().playSfx(SoundType.buttonClick);
  }
  
  void _pauseGame() {
    setState(() {
      _isPaused = !_isPaused;
    });
    
    if (_isPaused) {
      _gameTimer?.cancel();
    } else {
      _gameTimer = Timer.periodic(const Duration(milliseconds: 16), _updateGame);
    }
    
    SoundManager().playSfx(SoundType.buttonClick);
  }
  
  void _updateGame(Timer timer) {
    if (!_isPlaying || _isPaused || _gameOver) return;
    
    setState(() {
      // Update ball position
      _ballPosition = Offset(
        _ballPosition.dx + _ballVelocity.dx / _gameArea.width,
        _ballPosition.dy + _ballVelocity.dy / _gameArea.height,
      );
      
      // Ball collision with top/bottom walls
      if (_ballPosition.dy <= 0 || _ballPosition.dy >= 1) {
        _ballVelocity = Offset(_ballVelocity.dx, -_ballVelocity.dy);
        _ballPosition = Offset(
          _ballPosition.dx,
          _ballPosition.dy.clamp(0.0, 1.0),
        );
        SoundManager().playSfx(SoundType.buttonClick);
      }
      
      // Ball collision with paddles
      _checkPaddleCollisions();
      
      // Ball out of bounds (scoring)
      if (_ballPosition.dx < 0) {
        _aiScore++;
        _onScore();
      } else if (_ballPosition.dx > 1) {
        _playerScore++;
        _onScore();
      }
      
      // Update AI paddle
      _updateAI();
    });
  }
  
  void _checkPaddleCollisions() {
    final ballRect = Rect.fromCenter(
      center: Offset(
        _ballPosition.dx * _gameArea.width,
        _ballPosition.dy * _gameArea.height,
      ),
      width: _ballSize,
      height: _ballSize,
    );
    
    // Player paddle (left side)
    final playerPaddleRect = Rect.fromLTWH(
      20,
      _playerPaddleY * _gameArea.height - _paddleHeight / 2,
      _paddleWidth,
      _paddleHeight,
    );
    
    // AI paddle (right side)
    final aiPaddleRect = Rect.fromLTWH(
      _gameArea.width - 20 - _paddleWidth,
      _aiPaddleY * _gameArea.height - _paddleHeight / 2,
      _paddleWidth,
      _paddleHeight,
    );
    
    if (ballRect.overlaps(playerPaddleRect) && _ballVelocity.dx < 0) {
      _ballVelocity = Offset(-_ballVelocity.dx * 1.05, _ballVelocity.dy);
      SoundManager().playSfx(SoundType.buttonClick);
    } else if (ballRect.overlaps(aiPaddleRect) && _ballVelocity.dx > 0) {
      _ballVelocity = Offset(-_ballVelocity.dx * 1.05, _ballVelocity.dy);
      SoundManager().playSfx(SoundType.buttonClick);
    }
  }
  
  void _updateAI() {
    final ballY = _ballPosition.dy;
    final paddleY = _aiPaddleY;
    final diff = ballY - paddleY;
    
    if (diff.abs() > 0.02) {
      final moveSpeed = _paddleSpeed / _gameArea.height;
      _aiPaddleY += diff.sign * moveSpeed;
      _aiPaddleY = _aiPaddleY.clamp(0.1, 0.9);
    }
  }
  
  void _onScore() {
    _scoreController.forward().then((_) => _scoreController.reset());
    SoundManager().playSfx(SoundType.buttonClick);
    
    if (_playerScore >= _winningScore || _aiScore >= _winningScore) {
      _endGame();
    } else {
      _resetBall();
    }
  }
  
  void _endGame() {
    setState(() {
      _gameOver = true;
      _isPlaying = false;
    });
    _gameTimer?.cancel();
    
    if (_playerScore >= _winningScore) {
      SoundManager().playSfx(SoundType.reward);
    } else {
      SoundManager().playSfx(SoundType.buttonClick);
    }
  }
  
  void _movePaddle(double delta) {
    if (!_isPlaying || _isPaused) return;
    
    setState(() {
      _playerPaddleY += delta;
      _playerPaddleY = _playerPaddleY.clamp(0.1, 0.9);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: LayoutBuilder(
        builder: (context, constraints) {
          _gameArea = Size(constraints.maxWidth, constraints.maxHeight);

          return Stack(
            children: [
              // Animated background
              _buildAnimatedBackground(),

              // Game area
              if (_isPlaying && !_gameOver)
                _buildGameArea(),

              // UI overlay
              _buildUIOverlay(),

              // Game over overlay
              if (_gameOver)
                _buildGameOverOverlay(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black,
            NeonThemes.cyberBlue.primary.withValues(alpha: 0.03),
            NeonThemes.neonPink.primary.withValues(alpha: 0.03),
            Colors.black,
          ],
        ),
      ),
    );
  }

  Widget _buildGameArea() {
    return GestureDetector(
      onPanUpdate: (details) {
        final delta = details.delta.dy / _gameArea.height;
        _movePaddle(delta);
      },
      child: CustomPaint(
        painter: PongGamePainter(
          ballPosition: _ballPosition,
          playerPaddleY: _playerPaddleY,
          aiPaddleY: _aiPaddleY,
          ballGlow: _ballGlowController.value,
        ),
        size: _gameArea,
      ),
    );
  }

  Widget _buildUIOverlay() {
    return SafeArea(
      child: Column(
        children: [
          // Header with score and controls
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Back button
                NeonCircularButton(
                  icon: Icons.arrow_back,
                  glowColor: NeonThemes.cyberBlue.primary,
                  onPressed: () => Navigator.of(context).pop(),
                ),

                // Score display
                AnimatedBuilder(
                  animation: _scoreController,
                  builder: (context, child) {
                    final scale = 1.0 + (_scoreController.value * 0.3);
                    return Transform.scale(
                      scale: scale,
                      child: Row(
                        children: [
                          NeonText.title(
                            '$_playerScore',
                            glowColor: NeonThemes.electricGreen.primary,
                            fontSize: 32,
                          ),
                          const SizedBox(width: 20),
                          NeonText.body(
                            '-',
                            glowColor: NeonThemes.cyberBlue.primary,
                            fontSize: 24,
                          ),
                          const SizedBox(width: 20),
                          NeonText.title(
                            '$_aiScore',
                            glowColor: NeonThemes.neonPink.primary,
                            fontSize: 32,
                          ),
                        ],
                      ),
                    );
                  },
                ),

                // Pause button
                if (_isPlaying)
                  NeonCircularButton(
                    icon: _isPaused ? Icons.play_arrow : Icons.pause,
                    glowColor: NeonThemes.neonPink.primary,
                    onPressed: _pauseGame,
                  )
                else
                  const SizedBox(width: 48),
              ],
            ),
          ),

          const Spacer(),

          // Start game button
          if (!_isPlaying && !_gameOver)
            Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                children: [
                  NeonText.title(
                    'NEON PONG',
                    glowColor: NeonThemes.cyberBlue.primary,
                    fontSize: 48,
                  ),
                  const SizedBox(height: 20),
                  NeonText.body(
                    'Swipe to move your paddle',
                    glowColor: NeonThemes.cyberBlue.secondary,
                    fontSize: 16,
                  ),
                  const SizedBox(height: 40),
                  NeonButton.primary(
                    text: 'START GAME',
                    glowColor: NeonThemes.electricGreen.primary,
                    onPressed: _startGame,
                  ),
                ],
              ),
            ),

          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildGameOverOverlay() {
    final playerWon = _playerScore >= _winningScore;

    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: NeonContainer.panel(
          glowColor: playerWon
              ? NeonThemes.electricGreen.primary
              : NeonThemes.neonPink.primary,
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonText.title(
                  playerWon ? 'VICTORY!' : 'GAME OVER',
                  glowColor: playerWon
                      ? NeonThemes.electricGreen.primary
                      : NeonThemes.neonPink.primary,
                  fontSize: 36,
                ),
                const SizedBox(height: 20),
                NeonText.body(
                  'Final Score: $_playerScore - $_aiScore',
                  glowColor: NeonThemes.cyberBlue.primary,
                  fontSize: 18,
                ),
                const SizedBox(height: 30),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    NeonButton.secondary(
                      text: 'PLAY AGAIN',
                      glowColor: NeonThemes.electricGreen.primary,
                      onPressed: _resetGame,
                    ),
                    const SizedBox(width: 20),
                    NeonButton.secondary(
                      text: 'BACK',
                      glowColor: NeonThemes.cyberBlue.primary,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for the Pong game
class PongGamePainter extends CustomPainter {
  final Offset ballPosition;
  final double playerPaddleY;
  final double aiPaddleY;
  final double ballGlow;

  const PongGamePainter({
    required this.ballPosition,
    required this.playerPaddleY,
    required this.aiPaddleY,
    required this.ballGlow,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw center line
    _drawCenterLine(canvas, size);

    // Draw paddles
    _drawPaddle(canvas, size, true, playerPaddleY);
    _drawPaddle(canvas, size, false, aiPaddleY);

    // Draw ball
    _drawBall(canvas, size);
  }

  void _drawCenterLine(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = NeonThemes.cyberBlue.primary.withValues(alpha: 0.3)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final dashHeight = 10.0;
    final dashSpace = 10.0;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  void _drawPaddle(Canvas canvas, Size size, bool isPlayer, double paddleY) {
    final color = isPlayer
        ? NeonThemes.electricGreen.primary
        : NeonThemes.neonPink.primary;

    final x = isPlayer ? 20.0 : size.width - 20 - 12;
    final y = paddleY * size.height - 40;

    final rect = Rect.fromLTWH(x, y, 12, 80);

    // Draw glow effect
    final glowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0);

    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(6)),
      glowPaint,
    );

    // Draw paddle
    final paddlePaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(6)),
      paddlePaint,
    );
  }

  void _drawBall(Canvas canvas, Size size) {
    final center = Offset(
      ballPosition.dx * size.width,
      ballPosition.dy * size.height,
    );

    final color = NeonThemes.cyberBlue.primary;
    final glowIntensity = 0.5 + (ballGlow * 0.5);

    // Draw multiple glow layers
    for (int i = 3; i >= 0; i--) {
      final glowPaint = Paint()
        ..color = color.withValues(alpha: 0.2 * glowIntensity / (i + 1))
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, (i + 1) * 4.0);

      canvas.drawCircle(center, 6 + (i * 2), glowPaint);
    }

    // Draw ball
    final ballPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 6, ballPaint);
  }

  @override
  bool shouldRepaint(PongGamePainter oldDelegate) {
    return ballPosition != oldDelegate.ballPosition ||
           playerPaddleY != oldDelegate.playerPaddleY ||
           aiPaddleY != oldDelegate.aiPaddleY ||
           ballGlow != oldDelegate.ballGlow;
  }
}
