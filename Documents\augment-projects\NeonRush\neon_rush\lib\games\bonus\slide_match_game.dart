import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/game_provider.dart';
import '../../core/sound_manager.dart';
import '../../models/token_transaction.dart';

/// SlideMatch - Match-3 game with drag to swap tiles
class SlideMatchGame extends StatefulWidget {
  final Function(int score, int tokens)? onGameComplete;

  const SlideMatchGame({
    super.key,
    this.onGameComplete,
  });

  @override
  State<SlideMatchGame> createState() => _SlideMatchGameState();
}

class _SlideMatchGameState extends State<SlideMatchGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  
  List<List<MatchTile>> _grid = [];
  final List<AnimationController> _animationControllers = [];
  int _score = 0;
  int _moves = 30;
  int _combo = 0;
  bool _gameActive = false;
  int _target = 1000;
  MatchTile? _selectedTile;
  
  static const int gridSize = 6;
  static const List<Color> tileColors = [
    Color(0xFF00FFFF), // Cyan
    Color(0xFF00FF00), // Green
    Color(0xFFFFFF00), // Yellow
    Color(0xFFFF6B35), // Orange
    Color(0xFFFF00FF), // Magenta
    Color(0xFF8A2BE2), // Blue Violet
  ];
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _score = 0;
      _moves = 30;
      _combo = 0;
      _gameActive = true;
      _target = 1000;
      _selectedTile = null;
    });

    _initializeGrid();
    _startGameTimer();
  }

  void _initializeGrid() {
    _grid = List.generate(gridSize, (row) =>
        List.generate(gridSize, (col) => _createRandomTile(row, col)));
    
    // Ensure no initial matches
    _removeInitialMatches();
  }

  MatchTile _createRandomTile(int row, int col) {
    final random = Random();
    return MatchTile(
      row: row,
      col: col,
      color: tileColors[random.nextInt(tileColors.length)],
      type: random.nextInt(tileColors.length),
    );
  }

  void _removeInitialMatches() {
    bool hasMatches = true;
    while (hasMatches) {
      final matches = _findMatches();
      if (matches.isEmpty) {
        hasMatches = false;
      } else {
        for (final match in matches) {
          _grid[match.row][match.col] = _createRandomTile(match.row, match.col);
        }
      }
    }
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_gameActive) return;
      
      if (_moves <= 0 || _score >= _target) {
        _endGame();
      }
    });
  }

  List<MatchTile> _findMatches() {
    final matches = <MatchTile>[];
    
    // Check horizontal matches
    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize - 2; col++) {
        final tile1 = _grid[row][col];
        final tile2 = _grid[row][col + 1];
        final tile3 = _grid[row][col + 2];
        
        if (tile1.type == tile2.type && tile2.type == tile3.type && tile1.type != -1) {
          matches.addAll([tile1, tile2, tile3]);
          
          // Check for longer matches
          for (int i = col + 3; i < gridSize; i++) {
            if (_grid[row][i].type == tile1.type) {
              matches.add(_grid[row][i]);
            } else {
              break;
            }
          }
        }
      }
    }
    
    // Check vertical matches
    for (int col = 0; col < gridSize; col++) {
      for (int row = 0; row < gridSize - 2; row++) {
        final tile1 = _grid[row][col];
        final tile2 = _grid[row + 1][col];
        final tile3 = _grid[row + 2][col];
        
        if (tile1.type == tile2.type && tile2.type == tile3.type && tile1.type != -1) {
          matches.addAll([tile1, tile2, tile3]);
          
          // Check for longer matches
          for (int i = row + 3; i < gridSize; i++) {
            if (_grid[i][col].type == tile1.type) {
              matches.add(_grid[i][col]);
            } else {
              break;
            }
          }
        }
      }
    }
    
    return matches.toSet().toList(); // Remove duplicates
  }

  void _onTileTap(MatchTile tile) async {
    if (!_gameActive) return;
    
    if (_selectedTile == null) {
      // Select tile
      setState(() {
        _selectedTile = tile;
      });
      SoundManager().playSfx(SoundType.buttonClick);
    } else if (_selectedTile == tile) {
      // Deselect tile
      setState(() {
        _selectedTile = null;
      });
    } else if (_areAdjacent(_selectedTile!, tile)) {
      // Swap tiles
      await _swapTiles(_selectedTile!, tile);
    } else {
      // Select new tile
      setState(() {
        _selectedTile = tile;
      });
      SoundManager().playSfx(SoundType.buttonClick);
    }
  }

  bool _areAdjacent(MatchTile tile1, MatchTile tile2) {
    final rowDiff = (tile1.row - tile2.row).abs();
    final colDiff = (tile1.col - tile2.col).abs();
    return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1);
  }

  Future<void> _swapTiles(MatchTile tile1, MatchTile tile2) async {
    // Swap tiles in grid
    final temp = _grid[tile1.row][tile1.col];
    _grid[tile1.row][tile1.col] = _grid[tile2.row][tile2.col].copyWith(
      row: tile1.row,
      col: tile1.col,
    );
    _grid[tile2.row][tile2.col] = temp.copyWith(
      row: tile2.row,
      col: tile2.col,
    );
    
    setState(() {
      _selectedTile = null;
    });
    
    // Check for matches
    final matches = _findMatches();
    if (matches.isNotEmpty) {
      SoundManager().playSfx(SoundType.buttonClick);
      
      setState(() {
        _moves--;
      });
      
      await _processMatches(matches);
    } else {
      // No matches, swap back
      final temp = _grid[tile1.row][tile1.col];
      _grid[tile1.row][tile1.col] = _grid[tile2.row][tile2.col].copyWith(
        row: tile1.row,
        col: tile1.col,
      );
      _grid[tile2.row][tile2.col] = temp.copyWith(
        row: tile2.row,
        col: tile2.col,
      );
    }
  }

  Future<void> _processMatches(List<MatchTile> matches) async {
    // Calculate score
    final baseScore = matches.length * 10;
    final comboBonus = _combo * 5;
    final totalScore = baseScore + comboBonus;
    
    setState(() {
      _score += totalScore;
      _combo++;
    });
    
    // Remove matched tiles
    for (final match in matches) {
      _grid[match.row][match.col] = _createEmptyTile(match.row, match.col);
    }
    
    // Drop tiles down
    await _dropTiles();
    
    // Fill empty spaces
    _fillEmptySpaces();
    
    // Check for new matches
    final newMatches = _findMatches();
    if (newMatches.isNotEmpty) {
      await _processMatches(newMatches);
    } else {
      setState(() {
        _combo = 0;
      });
    }
  }

  MatchTile _createEmptyTile(int row, int col) {
    return MatchTile(
      row: row,
      col: col,
      color: Colors.transparent,
      type: -1,
    );
  }

  Future<void> _dropTiles() async {
    for (int col = 0; col < gridSize; col++) {
      final column = <MatchTile>[];
      
      // Collect non-empty tiles
      for (int row = gridSize - 1; row >= 0; row--) {
        if (_grid[row][col].type != -1) {
          column.add(_grid[row][col]);
        }
      }
      
      // Fill column from bottom
      for (int row = gridSize - 1; row >= 0; row--) {
        if (row >= gridSize - column.length) {
          final tileIndex = row - (gridSize - column.length);
          _grid[row][col] = column[tileIndex].copyWith(row: row, col: col);
        } else {
          _grid[row][col] = _createEmptyTile(row, col);
        }
      }
    }
  }

  void _fillEmptySpaces() {
    for (int col = 0; col < gridSize; col++) {
      for (int row = 0; row < gridSize; row++) {
        if (_grid[row][col].type == -1) {
          _grid[row][col] = _createRandomTile(row, col);
        }
      }
    }
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final tokens = _score ~/ 10; // 1 token per 10 points
    
    await gameProvider.earnTokens(
      tokens,
      TokenTransactionType.bonusGameReward,
      'SlideMatch game completed',
    );

    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, tokens);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            // Score and info
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text('Score: $_score', style: const TextStyle(color: Colors.white)),
                  Text('Moves: $_moves', style: const TextStyle(color: Colors.white)),
                  Text('Target: $_target', style: const TextStyle(color: Colors.white)),
                ],
              ),
            ),
            
            // Game grid
            Expanded(
              child: Center(
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    child: GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: gridSize,
                        crossAxisSpacing: 2,
                        mainAxisSpacing: 2,
                      ),
                      itemCount: gridSize * gridSize,
                      itemBuilder: (context, index) {
                        final row = index ~/ gridSize;
                        final col = index % gridSize;
                        final tile = _grid[row][col];
                        final isSelected = _selectedTile == tile;
                        
                        return GestureDetector(
                          onTap: () => _onTileTap(tile),
                          child: Container(
                            decoration: BoxDecoration(
                              color: tile.color,
                              border: Border.all(
                                color: isSelected ? Colors.white : Colors.grey,
                                width: isSelected ? 3 : 1,
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MatchTile {
  final int row;
  final int col;
  final Color color;
  final int type;

  const MatchTile({
    required this.row,
    required this.col,
    required this.color,
    required this.type,
  });

  MatchTile copyWith({
    int? row,
    int? col,
    Color? color,
    int? type,
  }) {
    return MatchTile(
      row: row ?? this.row,
      col: col ?? this.col,
      color: color ?? this.color,
      type: type ?? this.type,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MatchTile &&
        other.row == row &&
        other.col == col &&
        other.type == type;
  }

  @override
  int get hashCode => Object.hash(row, col, type);
}
