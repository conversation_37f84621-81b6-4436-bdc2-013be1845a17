import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../games/bonus/slide_match_game.dart';
import '../../core/sound_manager.dart';

class SlideMatchScreen extends StatefulWidget {
  const SlideMatchScreen({super.key});
  @override
  State<SlideMatchScreen> createState() => _SlideMatchScreenState();
}

class _SlideMatchScreenState extends State<SlideMatchScreen> {
  @override
  void initState() {
    super.initState();
    SoundManager().playGameMusic();
  }

  @override
  void dispose() {
    SoundManager().playMenuMusic();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return SlideMatchGame(
          onGameComplete: (score, tokens) {
            _showGameCompleteDialog(context, score, tokens);
          },
        );
      },
    );
  }

  void _showGameCompleteDialog(BuildContext context, int score, int tokens) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Game Complete!',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Final Score: $score',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              const SizedBox(height: 8),
              Text(
                'Tokens Earned: $tokens',
                style: const TextStyle(color: Color(0xFF00FFFF), fontSize: 16),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Return to bonus games
              },
              child: const Text(
                'Continue',
                style: TextStyle(color: Color(0xFF00FFFF)),
              ),
            ),
          ],
        );
      },
    );
  }
}
