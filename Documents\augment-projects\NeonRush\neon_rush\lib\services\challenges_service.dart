import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/challenge.dart';
import '../models/token_transaction.dart';
import 'token_service.dart';

/// Service for managing the comprehensive challenges system
class ChallengesService {
  static const String _activeChallengesKey = 'active_challenges';
  static const String _weekStartKey = 'challenges_week_start_date';
  static const String _challengeProgressKey = 'challenge_progress';
  static const String _completedChallengesKey = 'completed_challenges';

  /// Get current active challenges (5 per week)
  static Future<List<Challenge>> getActiveChallenges() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Check if we need to reset for a new week
    await _checkWeekReset();
    
    final challengesJson = prefs.getString(_activeChallengesKey);
    if (challengesJson == null) {
      // Generate new challenges for the week
      final challenges = await _generateWeeklyChallenges();
      await _saveActiveChallenges(challenges);
      return challenges;
    }
    
    final challengesData = jsonDecode(challengesJson) as List;
    return challengesData.map((data) => Challenge.fromJson(data)).toList();
  }

  /// Update challenge progress for a specific game action
  static Future<void> updateProgress({
    required String gameId,
    required ChallengeType type,
    int amount = 1,
    Map<String, dynamic>? metadata,
  }) async {
    final challenges = await getActiveChallenges();
    bool hasUpdates = false;

    for (int i = 0; i < challenges.length; i++) {
      final challenge = challenges[i];
      
      // Check if this challenge applies to this game and type
      if (_challengeApplies(challenge, gameId, type, metadata)) {
        final newProgress = (challenge.progress + amount).clamp(0, challenge.target);
        final wasCompleted = challenge.completed;
        final isNowCompleted = newProgress >= challenge.target;
        
        challenges[i] = challenge.copyWith(
          progress: newProgress,
          completed: isNowCompleted,
          completedAt: isNowCompleted && !wasCompleted ? DateTime.now() : challenge.completedAt,
        );
        
        hasUpdates = true;
        
        // Award tokens if just completed
        if (!wasCompleted && isNowCompleted) {
          await TokenService.earnTokens(
            amount: challenge.reward,
            type: TokenTransactionType.weeklyChallenge,
            description: 'Challenge Completed: ${challenge.title}',
            metadata: {'challengeId': challenge.id, 'gameId': gameId},
          );
          
          // Track completed challenge
          await _trackCompletedChallenge(challenge);
        }
      }
    }

    if (hasUpdates) {
      await _saveActiveChallenges(challenges);
    }
  }

  /// Check if a challenge applies to the current action
  static bool _challengeApplies(
    Challenge challenge,
    String gameId,
    ChallengeType type,
    Map<String, dynamic>? metadata,
  ) {
    // Check if challenge type matches
    if (challenge.type != type) return false;
    
    // Check if game is applicable
    if (!challenge.applicableGames.contains(gameId) && 
        !challenge.applicableGames.contains(GameIdentifiers.all)) {
      return false;
    }
    
    // Additional metadata checks for specific challenge types
    switch (challenge.type) {
      case ChallengeType.accuracy:
        final accuracy = metadata?['accuracy'] as double?;
        final requiredAccuracy = challenge.metadata['requiredAccuracy'] as double? ?? 1.0;
        return accuracy != null && accuracy >= requiredAccuracy;
        
      case ChallengeType.speed:
        final duration = metadata?['duration'] as int?;
        final maxDuration = challenge.metadata['maxDuration'] as int?;
        return duration != null && maxDuration != null && duration <= maxDuration;
        
      case ChallengeType.combo:
        final comboCount = metadata?['comboCount'] as int?;
        final minCombo = challenge.metadata['minCombo'] as int? ?? 1;
        return comboCount != null && comboCount >= minCombo;
        
      default:
        return true;
    }
  }

  /// Generate 5 random challenges from the pool for the week
  static Future<List<Challenge>> _generateWeeklyChallenges() async {
    final allChallenges = _getAllPossibleChallenges();
    final random = Random();
    
    // Ensure variety by selecting from different categories
    final selectedChallenges = <Challenge>[];
    final usedTypes = <ChallengeType>{};
    
    // First, try to get one challenge from each major category
    final categories = [
      ChallengeType.levelCompletion,
      ChallengeType.tokenEarning,
      ChallengeType.accuracy,
      ChallengeType.crossGame,
      ChallengeType.breakfinity,
    ];
    
    for (final category in categories) {
      final categoryOptions = allChallenges
          .where((c) => c.type == category && !usedTypes.contains(c.type))
          .toList();
      
      if (categoryOptions.isNotEmpty && selectedChallenges.length < 5) {
        final selected = categoryOptions[random.nextInt(categoryOptions.length)];
        selectedChallenges.add(selected);
        usedTypes.add(selected.type);
      }
    }
    
    // Fill remaining slots with random challenges
    while (selectedChallenges.length < 5) {
      final remaining = allChallenges
          .where((c) => !selectedChallenges.any((s) => s.id == c.id))
          .toList();
      
      if (remaining.isEmpty) break;
      
      final selected = remaining[random.nextInt(remaining.length)];
      selectedChallenges.add(selected);
    }
    
    return selectedChallenges;
  }

  /// Get all possible challenges (hundreds of them)
  static List<Challenge> _getAllPossibleChallenges() {
    final challenges = <Challenge>[];
    
    // NeonRush Level Completion Challenges
    challenges.addAll(_generateLevelCompletionChallenges());
    
    // Token Earning Challenges
    challenges.addAll(_generateTokenEarningChallenges());
    
    // Accuracy Challenges
    challenges.addAll(_generateAccuracyChallenges());
    
    // Speed Challenges
    challenges.addAll(_generateSpeedChallenges());
    
    // Combo Challenges
    challenges.addAll(_generateComboChallenges());
    
    // Breakfinity Challenges
    challenges.addAll(_generateBreakfinityChallenges());
    
    // Cross-Game Challenges
    challenges.addAll(_generateCrossGameChallenges());
    
    // Bonus Game Challenges
    challenges.addAll(_generateBonusGameChallenges());
    
    return challenges;
  }

  /// Check if week has reset and generate new challenges
  static Future<void> _checkWeekReset() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final weekStart = _getWeekStart(now);
    final weekStartString = weekStart.toIso8601String();
    
    final savedWeekStart = prefs.getString(_weekStartKey);
    
    if (savedWeekStart != weekStartString) {
      // New week, reset challenges
      await prefs.remove(_activeChallengesKey);
      await prefs.setString(_weekStartKey, weekStartString);
    }
  }

  /// Get the start of the current week (Monday)
  static DateTime _getWeekStart(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(date.year, date.month, date.day).subtract(Duration(days: daysFromMonday));
  }

  /// Save active challenges to storage
  static Future<void> _saveActiveChallenges(List<Challenge> challenges) async {
    final prefs = await SharedPreferences.getInstance();
    final challengesJson = jsonEncode(challenges.map((c) => c.toJson()).toList());
    await prefs.setString(_activeChallengesKey, challengesJson);
  }

  /// Track completed challenge for statistics
  static Future<void> _trackCompletedChallenge(Challenge challenge) async {
    final prefs = await SharedPreferences.getInstance();
    final completedJson = prefs.getString(_completedChallengesKey) ?? '[]';
    final completed = List<String>.from(jsonDecode(completedJson));
    completed.add(challenge.id);
    await prefs.setString(_completedChallengesKey, jsonEncode(completed));
  }

  /// Generate level completion challenges
  static List<Challenge> _generateLevelCompletionChallenges() {
    final challenges = <Challenge>[];

    // Basic level completion challenges
    for (int i = 1; i <= 20; i += 2) {
      challenges.add(Challenge(
        id: 'complete_levels_$i',
        title: 'Level Conqueror',
        description: 'Complete $i levels',
        reward: i * 10,
        progress: 0,
        target: i,
        icon: 'flag',
        completed: false,
        type: ChallengeType.levelCompletion,
        difficulty: i <= 5 ? ChallengeDifficulty.easy :
                   i <= 10 ? ChallengeDifficulty.medium :
                   i <= 15 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.neonrush],
      ));
    }

    // Specific level challenges
    for (int level = 1; level <= 10; level++) {
      challenges.add(Challenge(
        id: 'complete_level_$level',
        title: 'Level $level Master',
        description: 'Complete Level $level',
        reward: 25,
        progress: 0,
        target: 1,
        icon: 'target',
        completed: false,
        type: ChallengeType.levelCompletion,
        difficulty: level <= 3 ? ChallengeDifficulty.easy :
                   level <= 6 ? ChallengeDifficulty.medium :
                   level <= 8 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.neonrush],
        metadata: {'specificLevel': level},
      ));
    }

    return challenges;
  }

  /// Generate token earning challenges
  static List<Challenge> _generateTokenEarningChallenges() {
    final challenges = <Challenge>[];
    final amounts = [100, 250, 500, 750, 1000, 1500, 2000, 3000, 5000];

    for (final amount in amounts) {
      challenges.add(Challenge(
        id: 'earn_tokens_$amount',
        title: 'Token Collector',
        description: 'Earn $amount tokens',
        reward: (amount * 0.1).round(),
        progress: 0,
        target: amount,
        icon: 'monetization_on',
        completed: false,
        type: ChallengeType.tokenEarning,
        difficulty: amount <= 500 ? ChallengeDifficulty.easy :
                   amount <= 1500 ? ChallengeDifficulty.medium :
                   amount <= 3000 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.all],
      ));
    }

    return challenges;
  }

  /// Generate accuracy challenges
  static List<Challenge> _generateAccuracyChallenges() {
    final challenges = <Challenge>[];
    final accuracyTargets = [0.8, 0.85, 0.9, 0.95, 1.0];
    final counts = [1, 3, 5, 10, 15];

    for (final accuracy in accuracyTargets) {
      for (final count in counts) {
        final percentage = (accuracy * 100).round();
        challenges.add(Challenge(
          id: 'accuracy_${percentage}_$count',
          title: 'Precision Master',
          description: 'Achieve $percentage% accuracy in $count levels',
          reward: (percentage * count * 0.5).round(),
          progress: 0,
          target: count,
          icon: 'star',
          completed: false,
          type: ChallengeType.accuracy,
          difficulty: accuracy >= 0.95 ? ChallengeDifficulty.expert :
                     accuracy >= 0.9 ? ChallengeDifficulty.hard :
                     accuracy >= 0.85 ? ChallengeDifficulty.medium : ChallengeDifficulty.easy,
          applicableGames: [GameIdentifiers.neonrush],
          metadata: {'requiredAccuracy': accuracy},
        ));
      }
    }

    return challenges;
  }

  /// Generate speed challenges
  static List<Challenge> _generateSpeedChallenges() {
    final challenges = <Challenge>[];
    final timeLimits = [15, 20, 25, 30, 45, 60]; // seconds
    final counts = [1, 3, 5, 10];

    for (final timeLimit in timeLimits) {
      for (final count in counts) {
        challenges.add(Challenge(
          id: 'speed_${timeLimit}s_$count',
          title: 'Speed Demon',
          description: 'Complete $count levels in under ${timeLimit}s each',
          reward: (60 - timeLimit) * count,
          progress: 0,
          target: count,
          icon: 'speed',
          completed: false,
          type: ChallengeType.speed,
          difficulty: timeLimit <= 20 ? ChallengeDifficulty.expert :
                     timeLimit <= 30 ? ChallengeDifficulty.hard :
                     timeLimit <= 45 ? ChallengeDifficulty.medium : ChallengeDifficulty.easy,
          applicableGames: [GameIdentifiers.neonrush],
          metadata: {'maxDuration': timeLimit * 1000}, // Convert to milliseconds
        ));
      }
    }

    return challenges;
  }

  /// Generate combo challenges
  static List<Challenge> _generateComboChallenges() {
    final challenges = <Challenge>[];
    final comboSizes = [5, 10, 15, 20, 25, 30, 50, 75, 100];
    final counts = [1, 3, 5, 10, 20];

    for (final comboSize in comboSizes) {
      for (final count in counts) {
        challenges.add(Challenge(
          id: 'combo_${comboSize}_$count',
          title: 'Combo Master',
          description: 'Achieve $comboSize+ combo $count times',
          reward: comboSize * count,
          progress: 0,
          target: count,
          icon: 'combo',
          completed: false,
          type: ChallengeType.combo,
          difficulty: comboSize >= 50 ? ChallengeDifficulty.expert :
                     comboSize >= 25 ? ChallengeDifficulty.hard :
                     comboSize >= 15 ? ChallengeDifficulty.medium : ChallengeDifficulty.easy,
          applicableGames: [GameIdentifiers.neonrush],
          metadata: {'minCombo': comboSize},
        ));
      }
    }

    return challenges;
  }

  /// Generate Breakfinity challenges
  static List<Challenge> _generateBreakfinityChallenges() {
    final challenges = <Challenge>[];

    // Section destruction challenges
    final sectionCounts = [10, 25, 50, 100, 250, 500, 1000];
    for (final count in sectionCounts) {
      challenges.add(Challenge(
        id: 'breakfinity_sections_$count',
        title: 'Section Destroyer',
        description: 'Destroy $count sections in Breakfinity',
        reward: (count * 0.1).round(),
        progress: 0,
        target: count,
        icon: 'breakfinity',
        completed: false,
        type: ChallengeType.breakfinity,
        difficulty: count <= 50 ? ChallengeDifficulty.easy :
                   count <= 250 ? ChallengeDifficulty.medium :
                   count <= 500 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.breakfinity],
        metadata: {'actionType': 'sections_destroyed'},
      ));
    }

    // Layer progression challenges
    final layers = [1, 2, 3, 5, 10, 15, 20];
    for (final layer in layers) {
      challenges.add(Challenge(
        id: 'breakfinity_layer_$layer',
        title: 'Layer Breaker',
        description: 'Reach layer $layer in Breakfinity',
        reward: layer * 25,
        progress: 0,
        target: 1,
        icon: 'breakfinity',
        completed: false,
        type: ChallengeType.breakfinity,
        difficulty: layer <= 3 ? ChallengeDifficulty.easy :
                   layer <= 10 ? ChallengeDifficulty.medium :
                   layer <= 15 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.breakfinity],
        metadata: {'actionType': 'layer_reached', 'targetLayer': layer},
      ));
    }

    return challenges;
  }

  /// Generate cross-game challenges
  static List<Challenge> _generateCrossGameChallenges() {
    final challenges = <Challenge>[];

    // Play multiple games challenges
    challenges.add(const Challenge(
      id: 'play_all_games',
      title: 'Game Explorer',
      description: 'Play all 4 game modes',
      reward: 200,
      progress: 0,
      target: 4,
      icon: 'games',
      completed: false,
      type: ChallengeType.crossGame,
      difficulty: ChallengeDifficulty.medium,
      applicableGames: [GameIdentifiers.all],
      metadata: {'actionType': 'games_played'},
    ));

    // Daily activity challenges
    final days = [3, 5, 7, 14, 21, 30];
    for (final day in days) {
      challenges.add(Challenge(
        id: 'daily_activity_$day',
        title: 'Dedicated Player',
        description: 'Play for $day consecutive days',
        reward: day * 15,
        progress: 0,
        target: day,
        icon: 'streak',
        completed: false,
        type: ChallengeType.crossGame,
        difficulty: day <= 7 ? ChallengeDifficulty.easy :
                   day <= 14 ? ChallengeDifficulty.medium :
                   day <= 21 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.all],
        metadata: {'actionType': 'daily_streak'},
      ));
    }

    return challenges;
  }

  /// Generate bonus game challenges
  static List<Challenge> _generateBonusGameChallenges() {
    final challenges = <Challenge>[];

    // Bonus game completion challenges
    final counts = [1, 3, 5, 10, 15, 25];
    for (final count in counts) {
      challenges.add(Challenge(
        id: 'bonus_games_$count',
        title: 'Bonus Hunter',
        description: 'Complete $count bonus games',
        reward: count * 20,
        progress: 0,
        target: count,
        icon: 'bonus',
        completed: false,
        type: ChallengeType.bonusGame,
        difficulty: count <= 5 ? ChallengeDifficulty.easy :
                   count <= 15 ? ChallengeDifficulty.medium :
                   count <= 25 ? ChallengeDifficulty.hard : ChallengeDifficulty.expert,
        applicableGames: [GameIdentifiers.bonusGames],
      ));
    }

    // Lucky wheel challenges
    final spins = [1, 3, 5, 10, 20];
    for (final spin in spins) {
      challenges.add(Challenge(
        id: 'lucky_wheel_$spin',
        title: 'Lucky Spinner',
        description: 'Spin the lucky wheel $spin times',
        reward: spin * 10,
        progress: 0,
        target: spin,
        icon: 'bonus',
        completed: false,
        type: ChallengeType.bonusGame,
        difficulty: spin <= 5 ? ChallengeDifficulty.easy :
                   spin <= 10 ? ChallengeDifficulty.medium : ChallengeDifficulty.hard,
        applicableGames: [GameIdentifiers.luckyWheel],
      ));
    }

    return challenges;
  }

  /// Clear all challenge data (for testing/reset)
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_activeChallengesKey);
    await prefs.remove(_weekStartKey);
    await prefs.remove(_challengeProgressKey);
    await prefs.remove(_completedChallengesKey);
  }
}
