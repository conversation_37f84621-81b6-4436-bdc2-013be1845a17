import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants.dart';
import '../providers/game_provider.dart';
import '../models/mode_model.dart';

/// Carousel banner for home screen featuring daily challenges, streaks, and modes
class CarouselBanner extends StatefulWidget {
  const CarouselBanner({super.key});

  @override
  State<CarouselBanner> createState() => _CarouselBannerState();
}

class _CarouselBannerState extends State<CarouselBanner> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.9);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final bannerItems = _buildBannerItems(gameProvider);

        return Column(
          children: [
            SizedBox(
              height: 180,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: bannerItems.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: bannerItems[index],
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
            _buildIndicators(bannerItems.length),
          ],
        );
      },
    );
  }

  List<Widget> _buildBannerItems(GameProvider gameProvider) {
    return [
      _buildDailyChallengeCard(gameProvider),
      _buildLoginStreakCard(gameProvider),
      _buildFeaturedModeCard(gameProvider),
      _buildProgressCard(gameProvider),
    ];
  }

  Widget _buildDailyChallengeCard(GameProvider gameProvider) {
    return _BannerCard(
      title: 'Daily Challenge',
      subtitle: 'Complete today\'s challenge',
      description: 'Earn bonus tokens!',
      gradient: const LinearGradient(
        colors: [NeonColors.primaryAccent, NeonColors.electricBlue],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      icon: Icons.today,
      onTap: () {
        // Navigate to daily challenge
      },
    );
  }

  Widget _buildLoginStreakCard(GameProvider gameProvider) {
    final streak = gameProvider.loginStreak;
    
    return _BannerCard(
      title: 'Login Streak',
      subtitle: '$streak day${streak != 1 ? 's' : ''} in a row!',
      description: 'Keep it going for bigger rewards',
      gradient: const LinearGradient(
        colors: [NeonColors.neonGreen, NeonColors.highlights],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      icon: Icons.local_fire_department,
      onTap: () {
        // Show streak details
      },
    );
  }

  Widget _buildFeaturedModeCard(GameProvider gameProvider) {
    final unlockedModes = gameProvider.getAllModes().where((m) => m.unlocked).toList();
    final featuredMode = unlockedModes.isNotEmpty 
        ? unlockedModes.first 
        : GameModes.allModes.first;

    return _BannerCard(
      title: featuredMode.unlocked ? 'Play Now' : 'Unlock Mode',
      subtitle: featuredMode.name,
      description: featuredMode.description,
      gradient: const LinearGradient(
        colors: [NeonColors.secondaryAccent, NeonColors.purpleGlow],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      icon: featuredMode.unlocked ? Icons.play_arrow : Icons.lock,
      onTap: () {
        // Navigate to mode or unlock screen
      },
    );
  }

  Widget _buildProgressCard(GameProvider gameProvider) {
    // Show token progress instead of level progress since core game was removed
    final tokens = gameProvider.tokens;

    return _BannerCard(
      title: 'Your Tokens',
      subtitle: '$tokens Tokens Earned',
      description: 'Play games to earn more!',
      gradient: const LinearGradient(
        colors: [NeonColors.orangeFlame, NeonColors.highlights],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      icon: Icons.trending_up,
      child: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: Row(
          children: [
            Icon(
              Icons.stars,
              color: NeonColors.highlights,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Keep playing to earn more tokens!',
              style: GoogleFonts.orbitron(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        // Navigate to level selection
      },
    );
  }

  Widget _buildIndicators(int count) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        return Container(
          width: 8,
          height: 8,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? NeonColors.primaryAccent
                : NeonColors.textSecondary.withValues(alpha: 0.5),
            boxShadow: _currentIndex == index
                ? [
                    BoxShadow(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.6),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ]
                : null,
          ),
        );
      }),
    );
  }
}

/// Individual banner card widget
class _BannerCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final String description;
  final Gradient gradient;
  final IconData icon;
  final Widget? child;
  final VoidCallback? onTap;

  const _BannerCard({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.gradient,
    required this.icon,
    this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withValues(alpha: 0.3),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.5),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                color: Colors.black54,
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
              if (child != null) child!,
            ],
          ),
        ),
      ),
    )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 300))
        .slideX(
          begin: 0.2,
          end: 0,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOut,
        );
  }
}
