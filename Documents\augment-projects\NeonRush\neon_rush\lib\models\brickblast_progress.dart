import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Manages the overall progress and state of the BrickBlast game
class BrickBlastProgress {
  final int currentRound;
  final int highestRound;
  final int totalBricksDestroyed;
  final int totalTokensEarned;
  final int totalBallsLaunched;
  final int totalGamesPlayed;
  final int bestScore;
  final int bestRoundScore;
  final DateTime lastPlayedAt;
  final Map<String, int> statistics;
  final Map<String, dynamic> achievements;
  
  // Current game state
  final int currentScore;
  final int ballsRemaining;
  final bool isGameActive;
  final List<Map<String, dynamic>> currentBricks;
  final Map<String, dynamic> currentBall;

  BrickBlastProgress({
    this.currentRound = 1,
    this.highestRound = 1,
    this.totalBricksDestroyed = 0,
    this.totalTokensEarned = 0,
    this.totalBallsLaunched = 0,
    this.totalGamesPlayed = 0,
    this.bestScore = 0,
    this.bestRoundScore = 0,
    DateTime? lastPlayedAt,
    Map<String, int>? statistics,
    Map<String, dynamic>? achievements,
    this.currentScore = 0,
    this.ballsRemaining = 1,
    this.isGameActive = false,
    List<Map<String, dynamic>>? currentBricks,
    Map<String, dynamic>? currentBall,
  }) : lastPlayedAt = lastPlayedAt ?? DateTime.now(),
       statistics = statistics ?? {},
       achievements = achievements ?? {},
       currentBricks = currentBricks ?? [],
       currentBall = currentBall ?? {};

  /// Create a copy with modified values
  BrickBlastProgress copyWith({
    int? currentRound,
    int? highestRound,
    int? totalBricksDestroyed,
    int? totalTokensEarned,
    int? totalBallsLaunched,
    int? totalGamesPlayed,
    int? bestScore,
    int? bestRoundScore,
    DateTime? lastPlayedAt,
    Map<String, int>? statistics,
    Map<String, dynamic>? achievements,
    int? currentScore,
    int? ballsRemaining,
    bool? isGameActive,
    List<Map<String, dynamic>>? currentBricks,
    Map<String, dynamic>? currentBall,
  }) {
    return BrickBlastProgress(
      currentRound: currentRound ?? this.currentRound,
      highestRound: highestRound ?? this.highestRound,
      totalBricksDestroyed: totalBricksDestroyed ?? this.totalBricksDestroyed,
      totalTokensEarned: totalTokensEarned ?? this.totalTokensEarned,
      totalBallsLaunched: totalBallsLaunched ?? this.totalBallsLaunched,
      totalGamesPlayed: totalGamesPlayed ?? this.totalGamesPlayed,
      bestScore: bestScore ?? this.bestScore,
      bestRoundScore: bestRoundScore ?? this.bestRoundScore,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      statistics: statistics ?? Map.from(this.statistics),
      achievements: achievements ?? Map.from(this.achievements),
      currentScore: currentScore ?? this.currentScore,
      ballsRemaining: ballsRemaining ?? this.ballsRemaining,
      isGameActive: isGameActive ?? this.isGameActive,
      currentBricks: currentBricks ?? List.from(this.currentBricks),
      currentBall: currentBall ?? Map.from(this.currentBall),
    );
  }

  /// Update statistics
  BrickBlastProgress updateStatistics({
    int? bricksDestroyed,
    int? tokensEarned,
    int? ballsLaunched,
    int? gamesPlayed,
    int? score,
    int? roundScore,
    int? round,
  }) {
    final newStats = Map<String, int>.from(statistics);
    final newAchievements = Map<String, dynamic>.from(achievements);
    
    // Update totals
    final newTotalBricks = totalBricksDestroyed + (bricksDestroyed ?? 0);
    final newTotalTokens = totalTokensEarned + (tokensEarned ?? 0);
    final newTotalBalls = totalBallsLaunched + (ballsLaunched ?? 0);
    final newTotalGames = totalGamesPlayed + (gamesPlayed ?? 0);
    final newBestScore = score != null ? (score > bestScore ? score : bestScore) : bestScore;
    final newBestRoundScore = roundScore != null ? (roundScore > bestRoundScore ? roundScore : bestRoundScore) : bestRoundScore;
    final newHighestRound = round != null ? (round > highestRound ? round : highestRound) : highestRound;
    
    // Update specific statistics
    if (bricksDestroyed != null) {
      newStats['bricks_destroyed_session'] = (newStats['bricks_destroyed_session'] ?? 0) + bricksDestroyed;
    }
    if (tokensEarned != null) {
      newStats['tokens_earned_session'] = (newStats['tokens_earned_session'] ?? 0) + tokensEarned;
    }
    if (ballsLaunched != null) {
      newStats['balls_launched_session'] = (newStats['balls_launched_session'] ?? 0) + ballsLaunched;
    }
    
    // Check for achievements
    _checkAchievements(newTotalBricks, newTotalTokens, newTotalBalls, newTotalGames, newBestScore, newHighestRound, newAchievements);
    
    return copyWith(
      totalBricksDestroyed: newTotalBricks,
      totalTokensEarned: newTotalTokens,
      totalBallsLaunched: newTotalBalls,
      totalGamesPlayed: newTotalGames,
      bestScore: newBestScore,
      bestRoundScore: newBestRoundScore,
      highestRound: newHighestRound,
      statistics: newStats,
      achievements: newAchievements,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Check and unlock achievements
  void _checkAchievements(int totalBricks, int totalTokens, int totalBalls, int totalGames, int bestScore, int highestRound, Map<String, dynamic> achievements) {
    // Brick destruction achievements
    if (totalBricks >= 100 && !achievements.containsKey('brick_destroyer_100')) {
      achievements['brick_destroyer_100'] = DateTime.now().toIso8601String();
    }
    if (totalBricks >= 500 && !achievements.containsKey('brick_destroyer_500')) {
      achievements['brick_destroyer_500'] = DateTime.now().toIso8601String();
    }
    if (totalBricks >= 1000 && !achievements.containsKey('brick_destroyer_1000')) {
      achievements['brick_destroyer_1000'] = DateTime.now().toIso8601String();
    }
    
    // Token earning achievements
    if (totalTokens >= 500 && !achievements.containsKey('token_collector_500')) {
      achievements['token_collector_500'] = DateTime.now().toIso8601String();
    }
    if (totalTokens >= 2000 && !achievements.containsKey('token_collector_2000')) {
      achievements['token_collector_2000'] = DateTime.now().toIso8601String();
    }
    
    // Round progression achievements
    if (highestRound >= 10 && !achievements.containsKey('round_master_10')) {
      achievements['round_master_10'] = DateTime.now().toIso8601String();
    }
    if (highestRound >= 25 && !achievements.containsKey('round_master_25')) {
      achievements['round_master_25'] = DateTime.now().toIso8601String();
    }
    if (highestRound >= 50 && !achievements.containsKey('round_master_50')) {
      achievements['round_master_50'] = DateTime.now().toIso8601String();
    }
    
    // Score achievements
    if (bestScore >= 1000 && !achievements.containsKey('high_scorer_1000')) {
      achievements['high_scorer_1000'] = DateTime.now().toIso8601String();
    }
    if (bestScore >= 5000 && !achievements.containsKey('high_scorer_5000')) {
      achievements['high_scorer_5000'] = DateTime.now().toIso8601String();
    }
  }

  /// Reset session statistics
  BrickBlastProgress resetSession() {
    final newStats = Map<String, int>.from(statistics);
    newStats.remove('bricks_destroyed_session');
    newStats.remove('tokens_earned_session');
    newStats.remove('balls_launched_session');
    
    return copyWith(
      statistics: newStats,
      currentScore: 0,
      ballsRemaining: 1,
      isGameActive: false,
      currentBricks: [],
      currentBall: {},
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'currentRound': currentRound,
      'highestRound': highestRound,
      'totalBricksDestroyed': totalBricksDestroyed,
      'totalTokensEarned': totalTokensEarned,
      'totalBallsLaunched': totalBallsLaunched,
      'totalGamesPlayed': totalGamesPlayed,
      'bestScore': bestScore,
      'bestRoundScore': bestRoundScore,
      'lastPlayedAt': lastPlayedAt.toIso8601String(),
      'statistics': statistics,
      'achievements': achievements,
      'currentScore': currentScore,
      'ballsRemaining': ballsRemaining,
      'isGameActive': isGameActive,
      'currentBricks': currentBricks,
      'currentBall': currentBall,
    };
  }

  /// Create from JSON
  factory BrickBlastProgress.fromJson(Map<String, dynamic> json) {
    return BrickBlastProgress(
      currentRound: json['currentRound'] ?? 1,
      highestRound: json['highestRound'] ?? 1,
      totalBricksDestroyed: json['totalBricksDestroyed'] ?? 0,
      totalTokensEarned: json['totalTokensEarned'] ?? 0,
      totalBallsLaunched: json['totalBallsLaunched'] ?? 0,
      totalGamesPlayed: json['totalGamesPlayed'] ?? 0,
      bestScore: json['bestScore'] ?? 0,
      bestRoundScore: json['bestRoundScore'] ?? 0,
      lastPlayedAt: DateTime.parse(json['lastPlayedAt'] ?? DateTime.now().toIso8601String()),
      statistics: Map<String, int>.from(json['statistics'] ?? {}),
      achievements: Map<String, dynamic>.from(json['achievements'] ?? {}),
      currentScore: json['currentScore'] ?? 0,
      ballsRemaining: json['ballsRemaining'] ?? 1,
      isGameActive: json['isGameActive'] ?? false,
      currentBricks: List<Map<String, dynamic>>.from(json['currentBricks'] ?? []),
      currentBall: Map<String, dynamic>.from(json['currentBall'] ?? {}),
    );
  }

  /// Save progress to SharedPreferences
  Future<void> save() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('brickblast_progress', jsonEncode(toJson()));
  }

  /// Load progress from SharedPreferences
  static Future<BrickBlastProgress> load() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString('brickblast_progress');
    
    if (progressJson != null) {
      try {
        return BrickBlastProgress.fromJson(jsonDecode(progressJson));
      } catch (e) {
        // If loading fails, return default progress
        return BrickBlastProgress();
      }
    }
    
    return BrickBlastProgress();
  }
}
