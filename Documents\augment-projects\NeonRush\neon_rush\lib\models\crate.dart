import 'dart:math';
import '../core/constants.dart';


/// Represents a reward crate that can be opened
class Crate {
  final String id;
  final String name;
  final CrateType type;
  final String description;
  final String iconPath;
  final List<CrateReward> possibleRewards;
  final int guaranteedRewards;
  final double rareChance;
  final double epicChance;

  const Crate({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.iconPath,
    required this.possibleRewards,
    required this.guaranteedRewards,
    required this.rareChance,
    required this.epicChance,
  });

  /// Open the crate and get random rewards
  List<CrateReward> open() {
    final random = Random();
    final rewards = <CrateReward>[];
    
    // Add guaranteed rewards
    for (int i = 0; i < guaranteedRewards; i++) {
      final availableRewards = possibleRewards.where((r) => !rewards.contains(r)).toList();
      if (availableRewards.isNotEmpty) {
        final reward = availableRewards[random.nextInt(availableRewards.length)];
        rewards.add(reward);
      }
    }
    
    // Check for rare/epic bonus rewards
    if (random.nextDouble() < rareChance) {
      final rareRewards = possibleRewards.where((r) => r.rarity == RewardRarity.rare && !rewards.contains(r)).toList();
      if (rareRewards.isNotEmpty) {
        rewards.add(rareRewards[random.nextInt(rareRewards.length)]);
      }
    }
    
    if (random.nextDouble() < epicChance) {
      final epicRewards = possibleRewards.where((r) => r.rarity == RewardRarity.epic && !rewards.contains(r)).toList();
      if (epicRewards.isNotEmpty) {
        rewards.add(epicRewards[random.nextInt(epicRewards.length)]);
      }
    }
    
    return rewards;
  }

  /// Converts crate to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'description': description,
      'iconPath': iconPath,
      'possibleRewards': possibleRewards.map((r) => r.toJson()).toList(),
      'guaranteedRewards': guaranteedRewards,
      'rareChance': rareChance,
      'epicChance': epicChance,
    };
  }

  /// Creates crate from JSON
  factory Crate.fromJson(Map<String, dynamic> json) {
    return Crate(
      id: json['id'],
      name: json['name'],
      type: CrateType.values.firstWhere((t) => t.name == json['type']),
      description: json['description'],
      iconPath: json['iconPath'],
      possibleRewards: (json['possibleRewards'] as List)
          .map((r) => CrateReward.fromJson(r))
          .toList(),
      guaranteedRewards: json['guaranteedRewards'],
      rareChance: json['rareChance'],
      epicChance: json['epicChance'],
    );
  }
}

/// Represents a reward that can be found in a crate
class CrateReward {
  final String id;
  final String name;
  final RewardType type;
  final RewardRarity rarity;
  final dynamic value; // Can be int for tokens/xp, String for powerUp/theme IDs
  final int quantity;
  final String description;
  final String iconPath;

  const CrateReward({
    required this.id,
    required this.name,
    required this.type,
    required this.rarity,
    required this.value,
    required this.quantity,
    required this.description,
    required this.iconPath,
  });

  /// Converts reward to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'rarity': rarity.name,
      'value': value,
      'quantity': quantity,
      'description': description,
      'iconPath': iconPath,
    };
  }

  /// Creates reward from JSON
  factory CrateReward.fromJson(Map<String, dynamic> json) {
    return CrateReward(
      id: json['id'],
      name: json['name'],
      type: RewardType.values.firstWhere((t) => t.name == json['type']),
      rarity: RewardRarity.values.firstWhere((r) => r.name == json['rarity']),
      value: json['value'],
      quantity: json['quantity'],
      description: json['description'],
      iconPath: json['iconPath'],
    );
  }
}

/// Types of rewards
enum RewardType {
  tokens,
  xp,
  powerUp,
  theme,
  special
}

/// Rarity levels for rewards
enum RewardRarity {
  common,
  rare,
  epic,
  legendary
}

/// Predefined crates
class Crates {
  static final basicCrate = Crate(
    id: 'basic_crate',
    name: 'Basic Crate',
    type: CrateType.basic,
    description: 'Contains basic rewards and power-ups',
    iconPath: 'assets/images/crates/basic_crate.png',
    guaranteedRewards: 2,
    rareChance: 0.1,
    epicChance: 0.02,
    possibleRewards: [
      // Token rewards
      CrateReward(
        id: 'tokens_small',
        name: 'Small Token Bag',
        type: RewardType.tokens,
        rarity: RewardRarity.common,
        value: 25,
        quantity: 1,
        description: '25 Neon Tokens',
        iconPath: 'assets/images/rewards/tokens.png',
      ),
      CrateReward(
        id: 'tokens_medium',
        name: 'Token Pouch',
        type: RewardType.tokens,
        rarity: RewardRarity.rare,
        value: 50,
        quantity: 1,
        description: '50 Neon Tokens',
        iconPath: 'assets/images/rewards/tokens.png',
      ),
      // XP rewards
      CrateReward(
        id: 'xp_small',
        name: 'XP Boost',
        type: RewardType.xp,
        rarity: RewardRarity.common,
        value: 100,
        quantity: 1,
        description: '100 Experience Points',
        iconPath: 'assets/images/rewards/xp.png',
      ),
      // Power-up rewards
      CrateReward(
        id: 'shield_powerup',
        name: 'Shield',
        type: RewardType.powerUp,
        rarity: RewardRarity.common,
        value: 'shield',
        quantity: 1,
        description: 'Protective Shield Power-up',
        iconPath: 'assets/images/rewards/shield.png',
      ),
      CrateReward(
        id: 'time_boost_powerup',
        name: 'Time Boost',
        type: RewardType.powerUp,
        rarity: RewardRarity.common,
        value: 'time_boost',
        quantity: 1,
        description: 'Time Extension Power-up',
        iconPath: 'assets/images/rewards/time_boost.png',
      ),
    ],
  );

  static final advancedCrate = Crate(
    id: 'advanced_crate',
    name: 'Advanced Crate',
    type: CrateType.advanced,
    description: 'Contains rare power-ups and themes',
    iconPath: 'assets/images/crates/advanced_crate.png',
    guaranteedRewards: 3,
    rareChance: 0.3,
    epicChance: 0.1,
    possibleRewards: [
      // Better token rewards
      CrateReward(
        id: 'tokens_large',
        name: 'Token Chest',
        type: RewardType.tokens,
        rarity: RewardRarity.rare,
        value: 100,
        quantity: 1,
        description: '100 Neon Tokens',
        iconPath: 'assets/images/rewards/tokens.png',
      ),
      CrateReward(
        id: 'tokens_huge',
        name: 'Token Vault',
        type: RewardType.tokens,
        rarity: RewardRarity.epic,
        value: 200,
        quantity: 1,
        description: '200 Neon Tokens',
        iconPath: 'assets/images/rewards/tokens.png',
      ),
      // Better XP rewards
      CrateReward(
        id: 'xp_large',
        name: 'XP Surge',
        type: RewardType.xp,
        rarity: RewardRarity.rare,
        value: 300,
        quantity: 1,
        description: '300 Experience Points',
        iconPath: 'assets/images/rewards/xp.png',
      ),
      // Rare power-ups
      CrateReward(
        id: 'slow_motion_powerup',
        name: 'Slow Motion',
        type: RewardType.powerUp,
        rarity: RewardRarity.rare,
        value: 'slow_motion',
        quantity: 1,
        description: 'Slow Motion Power-up',
        iconPath: 'assets/images/rewards/slow_motion.png',
      ),
      CrateReward(
        id: 'magnet_touch_powerup',
        name: 'Magnet Touch',
        type: RewardType.powerUp,
        rarity: RewardRarity.rare,
        value: 'magnet_touch',
        quantity: 1,
        description: 'Magnet Touch Power-up',
        iconPath: 'assets/images/rewards/magnet_touch.png',
      ),
      // Theme rewards
      CrateReward(
        id: 'neon_pink_theme',
        name: 'Neon Pink Theme',
        type: RewardType.theme,
        rarity: RewardRarity.rare,
        value: 'neon_pink',
        quantity: 1,
        description: 'Unlock Neon Pink Theme',
        iconPath: 'assets/images/rewards/theme_pink.png',
      ),
    ],
  );

  static final epicCrate = Crate(
    id: 'epic_crate',
    name: 'Epic Crate',
    type: CrateType.epic,
    description: 'Contains epic rewards and exclusive items',
    iconPath: 'assets/images/crates/epic_crate.png',
    guaranteedRewards: 4,
    rareChance: 0.5,
    epicChance: 0.3,
    possibleRewards: [
      // Epic token rewards
      CrateReward(
        id: 'tokens_massive',
        name: 'Token Treasure',
        type: RewardType.tokens,
        rarity: RewardRarity.epic,
        value: 500,
        quantity: 1,
        description: '500 Neon Tokens',
        iconPath: 'assets/images/rewards/tokens.png',
      ),
      // Epic XP rewards
      CrateReward(
        id: 'xp_massive',
        name: 'XP Explosion',
        type: RewardType.xp,
        rarity: RewardRarity.epic,
        value: 1000,
        quantity: 1,
        description: '1000 Experience Points',
        iconPath: 'assets/images/rewards/xp.png',
      ),
      // Epic power-ups
      CrateReward(
        id: 'neon_bomb_powerup',
        name: 'Neon Bomb',
        type: RewardType.powerUp,
        rarity: RewardRarity.epic,
        value: 'neon_bomb',
        quantity: 2,
        description: '2x Neon Bomb Power-ups',
        iconPath: 'assets/images/rewards/neon_bomb.png',
      ),
      // Epic themes
      CrateReward(
        id: 'rainbow_theme',
        name: 'Rainbow Chaos Theme',
        type: RewardType.theme,
        rarity: RewardRarity.legendary,
        value: 'rainbow',
        quantity: 1,
        description: 'Unlock Rainbow Chaos Theme',
        iconPath: 'assets/images/rewards/theme_rainbow.png',
      ),
      // Special rewards
      CrateReward(
        id: 'level_skip',
        name: 'Level Skip',
        type: RewardType.special,
        rarity: RewardRarity.legendary,
        value: 'level_skip',
        quantity: 1,
        description: 'Skip any level instantly',
        iconPath: 'assets/images/rewards/level_skip.png',
      ),
    ],
  );

  /// Get all available crates
  static List<Crate> get allCrates => [
        basicCrate,
        advancedCrate,
        epicCrate,
      ];

  /// Get crate by ID
  static Crate? getCrateById(String id) {
    try {
      return allCrates.firstWhere((crate) => crate.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get crate by type
  static Crate? getCrateByType(CrateType type) {
    try {
      return allCrates.firstWhere((crate) => crate.type == type);
    } catch (e) {
      return null;
    }
  }
}
