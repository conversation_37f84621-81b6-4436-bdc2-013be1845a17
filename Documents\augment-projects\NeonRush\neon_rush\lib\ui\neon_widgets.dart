// Export all neon UI widgets for easy importing
export 'neon_effects.dart';
export 'neon_text.dart';
export 'neon_button.dart';
export 'neon_container.dart';

import 'package:flutter/material.dart';
import 'neon_effects.dart';
import 'neon_text.dart';

/// A progress bar with neon glow effects
class NeonProgressBar extends StatelessWidget {
  final double value;
  final double width;
  final double height;
  final Color glowColor;
  final Color? backgroundColor;
  final Color? fillColor;
  final double borderRadius;
  final bool animate;
  final String? label;

  const NeonProgressBar({
    super.key,
    required this.value,
    this.width = 200.0,
    this.height = 20.0,
    required this.glowColor,
    this.backgroundColor,
    this.fillColor,
    this.borderRadius = 10.0,
    this.animate = true,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? 
        glowColor.withValues(alpha: 0.1);
    final effectiveFillColor = fillColor ?? glowColor;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          NeonText.caption(
            label!,
            glowColor: glowColor,
          ),
          const SizedBox(height: 8.0),
        ],
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: glowColor.withValues(alpha: 0.5),
              width: 1.0,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius - 1),
            child: Stack(
              children: [
                // Fill
                AnimatedContainer(
                  duration: animate 
                      ? const Duration(milliseconds: 500)
                      : Duration.zero,
                  width: width * value.clamp(0.0, 1.0),
                  height: height,
                  decoration: BoxDecoration(
                    color: effectiveFillColor,
                    boxShadow: NeonEffects.createGlow(
                      color: effectiveFillColor,
                      intensity: 0.8,
                    ),
                  ),
                ),
                // Shimmer effect
                if (animate && value > 0)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Colors.transparent,
                            Colors.white.withValues(alpha: 0.3),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ).withShimmer(),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A slider with neon glow effects
class NeonSlider extends StatefulWidget {
  final double value;
  final ValueChanged<double> onChanged;
  final double min;
  final double max;
  final Color glowColor;
  final double width;
  final String? label;

  const NeonSlider({
    super.key,
    required this.value,
    required this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    required this.glowColor,
    this.width = 200.0,
    this.label,
  });

  @override
  State<NeonSlider> createState() => _NeonSliderState();
}

class _NeonSliderState extends State<NeonSlider> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          NeonText.caption(
            widget.label!,
            glowColor: widget.glowColor,
          ),
          const SizedBox(height: 8.0),
        ],
        SizedBox(
          width: widget.width,
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: widget.glowColor,
              inactiveTrackColor: widget.glowColor.withValues(alpha: 0.3),
              thumbColor: widget.glowColor,
              overlayColor: widget.glowColor.withValues(alpha: 0.2),
              thumbShape: _NeonThumbShape(glowColor: widget.glowColor),
              trackShape: _NeonTrackShape(glowColor: widget.glowColor),
              trackHeight: 4.0,
            ),
            child: Slider(
              value: widget.value,
              min: widget.min,
              max: widget.max,
              onChanged: widget.onChanged,
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom thumb shape for neon slider
class _NeonThumbShape extends SliderComponentShape {
  final Color glowColor;

  _NeonThumbShape({required this.glowColor});

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return const Size(20.0, 20.0);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;
    
    // Draw glow
    final glowPaint = Paint()
      ..color = glowColor.withValues(alpha: 0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8.0);
    canvas.drawCircle(center, 15.0, glowPaint);
    
    // Draw thumb
    final thumbPaint = Paint()
      ..color = glowColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 10.0, thumbPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawCircle(center, 10.0, borderPaint);
  }
}

/// Custom track shape for neon slider
class _NeonTrackShape extends SliderTrackShape {
  final Color glowColor;

  _NeonTrackShape({required this.glowColor});

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final trackHeight = sliderTheme.trackHeight!;
    final trackLeft = offset.dx;
    final trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 2,
  }) {
    final canvas = context.canvas;
    final trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // Draw inactive track
    final inactiveTrackPaint = Paint()
      ..color = glowColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, const Radius.circular(2.0)),
      inactiveTrackPaint,
    );

    // Draw active track
    final activeTrackRect = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      thumbCenter.dx,
      trackRect.bottom,
    );
    
    final activeTrackPaint = Paint()
      ..color = glowColor
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);
    canvas.drawRRect(
      RRect.fromRectAndRadius(activeTrackRect, const Radius.circular(2.0)),
      activeTrackPaint,
    );
  }
}

/// A loading spinner with neon glow effects
class NeonLoadingSpinner extends StatefulWidget {
  final Color glowColor;
  final double size;
  final double strokeWidth;
  final Duration duration;

  const NeonLoadingSpinner({
    super.key,
    required this.glowColor,
    this.size = 50.0,
    this.strokeWidth = 4.0,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<NeonLoadingSpinner> createState() => _NeonLoadingSpinnerState();
}

class _NeonLoadingSpinnerState extends State<NeonLoadingSpinner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.rotate(
            angle: _controller.value * 2 * 3.14159,
            child: CustomPaint(
              painter: _NeonSpinnerPainter(
                glowColor: widget.glowColor,
                strokeWidth: widget.strokeWidth,
                progress: _controller.value,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Custom painter for neon loading spinner
class _NeonSpinnerPainter extends CustomPainter {
  final Color glowColor;
  final double strokeWidth;
  final double progress;

  _NeonSpinnerPainter({
    required this.glowColor,
    required this.strokeWidth,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Draw glow
    final glowPaint = Paint()
      ..color = glowColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 2
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);
    canvas.drawCircle(center, radius, glowPaint);

    // Draw main arc
    final paint = Paint()
      ..color = glowColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    const sweepAngle = 3.14159; // 180 degrees
    final startAngle = progress * 2 * 3.14159;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
