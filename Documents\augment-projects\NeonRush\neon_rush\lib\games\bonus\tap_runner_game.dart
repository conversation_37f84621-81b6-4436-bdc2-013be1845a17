import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';

import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// TapRunner - Tap to jump over obstacles while auto-running
class TapRunnerGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const TapRunnerGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<TapRunnerGame> createState() => _TapRunnerGameState();
}

class _TapRunnerGameState extends State<TapRunnerGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _obstacleTimer;
  late AnimationController _jumpController;
  late Animation<double> _jumpAnimation;
  
  final List<Obstacle> _obstacles = [];
  final List<Platform> _platforms = [];
  double _playerY = 0;
  double _groundY = 0;
  bool _isJumping = false;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _score = 0;
  double _gameSpeed = 200.0; // pixels per second
  double _backgroundOffset = 0;
  int _level = 1;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _obstacleTimer.cancel();
    _jumpController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _jumpController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _jumpAnimation = Tween<double>(
      begin: 0.0,
      end: -120.0, // Jump height
    ).animate(CurvedAnimation(
      parent: _jumpController,
      curve: Curves.easeOutQuad,
    ));

    _jumpController.addListener(() {
      setState(() {
        _playerY = _groundY + _jumpAnimation.value;
      });
    });

    _jumpController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _jumpController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        setState(() {
          _isJumping = false;
        });
      }
    });
  }

  void _startNewGame() {
    setState(() {
      _obstacles.clear();
      _platforms.clear();
      _score = 0;
      _gameActive = true;
      _gameStarted = true;
      _gameSpeed = 200.0;
      _backgroundOffset = 0;
      _level = 1;
      _groundY = MediaQuery.of(context).size.height - 150;
      _playerY = _groundY;
      _isJumping = false;
    });

    _generateInitialPlatforms();
    _startGameLoop();
    _startObstacleSpawning();
  }

  void _generateInitialPlatforms() {
    
    // Generate ground platforms
    for (int i = 0; i < 10; i++) {
      _platforms.add(Platform(
        x: i * 100.0,
        y: _groundY + 30,
        width: 100,
        height: 20,
        color: const Color(0xFF00FFFF),
      ));
    }
  }

  void _startGameLoop() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateGame();
    });
  }

  void _startObstacleSpawning() {
    _obstacleTimer = Timer.periodic(const Duration(milliseconds: 2000), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnObstacle();
    });
  }

  void _updateGame() {
    final deltaTime = 16 / 1000.0; // 16ms in seconds
    final screenWidth = MediaQuery.of(context).size.width;
    
    setState(() {
      // Update background
      _backgroundOffset += _gameSpeed * deltaTime;
      
      // Update score based on distance
      _score = (_backgroundOffset / 10).round();
      
      // Increase speed over time
      if (_score > 0 && _score % 100 == 0) {
        _gameSpeed = min(400, _gameSpeed + 20);
        _level = (_score ~/ 100) + 1;
      }
      
      // Update obstacles
      for (int i = _obstacles.length - 1; i >= 0; i--) {
        final obstacle = _obstacles[i];
        final newX = obstacle.x - (_gameSpeed * deltaTime);
        _obstacles[i] = obstacle.copyWith(x: newX);
        
        // Remove obstacles that have moved off screen
        if (newX < -obstacle.width) {
          _obstacles.removeAt(i);
        }
      }
      
      // Update platforms
      for (int i = _platforms.length - 1; i >= 0; i--) {
        final platform = _platforms[i];
        final newX = platform.x - (_gameSpeed * deltaTime);
        _platforms[i] = platform.copyWith(x: newX);
        
        // Remove platforms that have moved off screen
        if (newX < -platform.width) {
          _platforms.removeAt(i);
        }
      }
      
      // Add new platforms as needed
      if (_platforms.isNotEmpty && _platforms.last.x < screenWidth) {
        _platforms.add(Platform(
          x: _platforms.last.x + 100,
          y: _groundY + 30,
          width: 100,
          height: 20,
          color: const Color(0xFF00FFFF),
        ));
      }
    });
    
    // Check collisions
    _checkCollisions();
  }

  void _spawnObstacle() {
    final screenWidth = MediaQuery.of(context).size.width;
    final random = Random();
    
    // Random obstacle type
    final isSpike = random.nextBool();
    
    final obstacle = Obstacle(
      x: screenWidth + 50,
      y: isSpike ? _groundY - 40 : _groundY - 80,
      width: 30,
      height: isSpike ? 40 : 80,
      isSpike: isSpike,
      color: const Color(0xFFFF0000),
    );
    
    setState(() {
      _obstacles.add(obstacle);
    });
  }

  void _jump() async {
    if (!_gameActive || _isJumping) return;
    
    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();
    
    setState(() {
      _isJumping = true;
    });
    
    _jumpController.forward();
  }

  void _checkCollisions() {
    const playerWidth = 40.0;
    const playerHeight = 60.0;
    final playerRect = Rect.fromLTWH(
      100 - playerWidth / 2, // Player is always at x=100
      _playerY - playerHeight,
      playerWidth,
      playerHeight,
    );
    
    // Check collision with obstacles
    for (final obstacle in _obstacles) {
      final obstacleRect = Rect.fromLTWH(
        obstacle.x,
        obstacle.y,
        obstacle.width,
        obstacle.height,
      );
      
      if (playerRect.overlaps(obstacleRect)) {
        _endGame();
        return;
      }
    }
    
    // Check if player fell off platform
    bool onPlatform = false;
    for (final platform in _platforms) {
      final platformRect = Rect.fromLTWH(
        platform.x,
        platform.y,
        platform.width,
        platform.height,
      );
      
      if (playerRect.bottom >= platformRect.top &&
          playerRect.bottom <= platformRect.bottom &&
          playerRect.right > platformRect.left &&
          playerRect.left < platformRect.right) {
        onPlatform = true;
        break;
      }
    }
    
    if (!onPlatform && !_isJumping && _playerY >= _groundY) {
      _endGame();
    }
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    if (!mounted) return;

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 5); // 1 token per 5 points

    // Call the completion callback instead of showing dialog directly
    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, _score ~/ 5);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onTap: _jump,
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: CustomPaint(
                  painter: TapRunnerPainter(
                    platforms: _platforms,
                    obstacles: _obstacles,
                    playerY: _playerY,
                    backgroundOffset: _backgroundOffset,
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Distance: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: NeonText.body(
                    'TAP TO JUMP',
                    glowColor: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents an obstacle
class Obstacle {
  final double x;
  final double y;
  final double width;
  final double height;
  final bool isSpike;
  final Color color;

  const Obstacle({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.isSpike,
    required this.color,
  });

  Obstacle copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    bool? isSpike,
    Color? color,
  }) {
    return Obstacle(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      isSpike: isSpike ?? this.isSpike,
      color: color ?? this.color,
    );
  }
}

/// Represents a platform
class Platform {
  final double x;
  final double y;
  final double width;
  final double height;
  final Color color;

  const Platform({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
  });

  Platform copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    Color? color,
  }) {
    return Platform(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      color: color ?? this.color,
    );
  }
}

/// Custom painter for the tap runner game
class TapRunnerPainter extends CustomPainter {
  final List<Platform> platforms;
  final List<Obstacle> obstacles;
  final double playerY;
  final double backgroundOffset;

  TapRunnerPainter({
    required this.platforms,
    required this.obstacles,
    required this.playerY,
    required this.backgroundOffset,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background grid
    _drawBackground(canvas, size);
    
    // Draw platforms
    for (final platform in platforms) {
      _drawPlatform(canvas, platform);
    }
    
    // Draw obstacles
    for (final obstacle in obstacles) {
      _drawObstacle(canvas, obstacle);
    }
    
    // Draw player
    _drawPlayer(canvas);
  }

  void _drawBackground(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.1)
      ..strokeWidth = 1;
    
    final gridSize = 50.0;
    final offsetX = backgroundOffset % gridSize;
    
    // Draw vertical lines
    for (double x = -offsetX; x < size.width + gridSize; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
    
    // Draw horizontal lines
    for (double y = 0; y < size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  void _drawPlatform(Canvas canvas, Platform platform) {
    final rect = Rect.fromLTWH(platform.x, platform.y, platform.width, platform.height);
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = platform.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    
    canvas.drawRect(rect, glowPaint);
    
    // Draw main platform
    final platformPaint = Paint()
      ..color = platform.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(rect, platformPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = platform.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRect(rect, borderPaint);
  }

  void _drawObstacle(Canvas canvas, Obstacle obstacle) {
    final rect = Rect.fromLTWH(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = obstacle.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);
    
    canvas.drawRect(rect, glowPaint);
    
    // Draw main obstacle
    final obstaclePaint = Paint()
      ..color = obstacle.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    if (obstacle.isSpike) {
      // Draw spike shape
      final path = Path();
      path.moveTo(obstacle.x + obstacle.width / 2, obstacle.y);
      path.lineTo(obstacle.x, obstacle.y + obstacle.height);
      path.lineTo(obstacle.x + obstacle.width, obstacle.y + obstacle.height);
      path.close();
      canvas.drawPath(path, obstaclePaint);
      
      final borderPaint = Paint()
        ..color = obstacle.color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawPath(path, borderPaint);
    } else {
      // Draw rectangular obstacle
      canvas.drawRect(rect, obstaclePaint);
      
      final borderPaint = Paint()
        ..color = obstacle.color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawRect(rect, borderPaint);
    }
  }

  void _drawPlayer(Canvas canvas) {
    const playerX = 100.0;
    const playerWidth = 40.0;
    const playerHeight = 60.0;

    final playerRect = Rect.fromLTWH(
      playerX - playerWidth / 2,
      playerY - playerHeight,
      playerWidth,
      playerHeight,
    );

    // Draw glow effect
    final glowPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawRRect(
      RRect.fromRectAndRadius(playerRect, const Radius.circular(8)),
      glowPaint,
    );

    // Draw main player
    final playerPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(playerRect, const Radius.circular(8)),
      playerPaint,
    );

    // Draw border
    final borderPaint = Paint()
      ..color = const Color(0xFF00FF00)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawRRect(
      RRect.fromRectAndRadius(playerRect, const Radius.circular(8)),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
