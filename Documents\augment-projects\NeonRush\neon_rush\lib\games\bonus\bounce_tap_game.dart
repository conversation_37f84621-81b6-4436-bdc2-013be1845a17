import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';


import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';
import '../../providers/game_provider.dart';
import '../../models/token_transaction.dart';

/// BounceTap - Tap to keep a neon ball bouncing on rising platforms
class BounceTapGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const BounceTapGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<BounceTapGame> createState() => _BounceTapGameState();
}

class _BounceTapGameState extends State<BounceTapGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _platformTimer;
  
  final List<Platform> _platforms = [];
  final List<ParticleEffect> _particles = [];
  double _ballX = 200;
  double _ballY = 300;
  double _ballVelocityX = 0;
  double _ballVelocityY = 0;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _score = 0;
  int _level = 1;
  double _gravity = 500; // pixels per second squared
  double _bounceForce = -300; // upward velocity when bouncing
  double _platformSpeed = 50; // pixels per second upward
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _platformTimer.cancel();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _platforms.clear();
      _particles.clear();
      _score = 0;
      _level = 1;
      _gameActive = true;
      _gameStarted = true;
      _ballX = 200;
      _ballY = 300;
      _ballVelocityX = 0;
      _ballVelocityY = 0;
      _gravity = 500;
      _bounceForce = -300;
      _platformSpeed = 50;
    });

    _generateInitialPlatforms();
    _startGameLoop();
    _startPlatformSpawning();
  }

  void _generateInitialPlatforms() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // Generate initial platforms
    for (int i = 0; i < 5; i++) {
      final platform = Platform(
        x: Random().nextDouble() * (screenWidth - 100),
        y: screenHeight - 100 - (i * 150),
        width: 100 + Random().nextDouble() * 50,
        height: 20,
        color: _getPlatformColor(i),
      );
      _platforms.add(platform);
    }
  }

  Color _getPlatformColor(int index) {
    final colors = [
      const Color(0xFF00FFFF),
      const Color(0xFF00FF00),
      const Color(0xFFFFFF00),
      const Color(0xFFFF6B35),
      const Color(0xFFFF00FF),
    ];
    return colors[index % colors.length];
  }

  void _startGameLoop() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateGame();
    });
  }

  void _startPlatformSpawning() {
    _platformTimer = Timer.periodic(const Duration(milliseconds: 2000), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnPlatform();
    });
  }

  void _updateGame() {
    final deltaTime = 16 / 1000.0; // 16ms in seconds
    final screenWidth = MediaQuery.of(context).size.width;
    
    setState(() {
      // Update ball physics
      _ballVelocityY += _gravity * deltaTime;
      _ballX += _ballVelocityX * deltaTime;
      _ballY += _ballVelocityY * deltaTime;
      
      // Ball horizontal boundaries
      if (_ballX <= 15) {
        _ballX = 15;
        _ballVelocityX = -_ballVelocityX * 0.8;
      } else if (_ballX >= screenWidth - 15) {
        _ballX = screenWidth - 15;
        _ballVelocityX = -_ballVelocityX * 0.8;
      }
      
      // Move platforms up
      for (int i = _platforms.length - 1; i >= 0; i--) {
        final platform = _platforms[i];
        final newY = platform.y - (_platformSpeed * deltaTime);
        _platforms[i] = platform.copyWith(y: newY);
        
        // Remove platforms that have moved off screen
        if (newY < -50) {
          _platforms.removeAt(i);
        }
      }
      
      // Update particles
      for (int i = _particles.length - 1; i >= 0; i--) {
        final particle = _particles[i];
        if (DateTime.now().difference(particle.createdAt).inMilliseconds > 1000) {
          _particles.removeAt(i);
        }
      }
      
      // Increase difficulty over time
      if (_score > 0 && _score % 10 == 0) {
        _platformSpeed = min(100, _platformSpeed + 5);
        _level = (_score ~/ 10) + 1;
      }
    });
    
    // Check collisions
    _checkCollisions();
    
    // Check game over
    if (_ballY > MediaQuery.of(context).size.height + 50) {
      _endGame();
    }
  }

  void _spawnPlatform() {
    final screenWidth = MediaQuery.of(context).size.width;
    final random = Random();
    
    final platform = Platform(
      x: random.nextDouble() * (screenWidth - 100),
      y: -50,
      width: 80 + random.nextDouble() * 60,
      height: 20,
      color: _getPlatformColor(_platforms.length),
    );
    
    setState(() {
      _platforms.add(platform);
    });
  }

  void _checkCollisions() {
    const ballRadius = 15.0;
    
    for (final platform in _platforms) {
      // Check if ball is colliding with platform from above
      if (_ballY + ballRadius >= platform.y &&
          _ballY + ballRadius <= platform.y + platform.height + 10 &&
          _ballX >= platform.x - ballRadius &&
          _ballX <= platform.x + platform.width + ballRadius &&
          _ballVelocityY > 0) {
        
        // Bounce off platform
        _bounceOffPlatform(platform);
        break;
      }
    }
  }

  void _bounceOffPlatform(Platform platform) async {
    setState(() {
      _ballY = platform.y - 15;
      _ballVelocityY = _bounceForce;
      _score += 1;
      
      // Add particle effect
      _particles.add(ParticleEffect(
        x: _ballX,
        y: _ballY,
        color: platform.color,
        createdAt: DateTime.now(),
      ));
    });

    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();
  }

  void _onTap() async {
    if (!_gameActive) return;
    
    // Give ball upward boost
    setState(() {
      _ballVelocityY = min(_ballVelocityY, _bounceForce * 0.7);
    });

    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    final tokensEarned = _score ~/ 3; // 1 token per 3 points
    if (mounted) {
      final gameProvider = context.read<GameProvider>();
      await gameProvider.earnTokens(
        tokensEarned,
        TokenTransactionType.bonusGameReward,
        'Bounce Tap completed',
        metadata: {'score': _score, 'tokensEarned': tokensEarned},
      );
    }

    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 3}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onTap: _onTap,
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: CustomPaint(
                  painter: BounceTapPainter(
                    platforms: _platforms,
                    particles: _particles,
                    ballX: _ballX,
                    ballY: _ballY,
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Speed: ${_platformSpeed.toInt()}',
                        glowColor: const Color(0xFF00FF00),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'TAP TO BOOST THE BALL',
                        glowColor: Colors.white,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 8),
                      NeonText.body(
                        'BOUNCE ON PLATFORMS TO SCORE',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a platform
class Platform {
  final double x;
  final double y;
  final double width;
  final double height;
  final Color color;

  const Platform({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
  });

  Platform copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    Color? color,
  }) {
    return Platform(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      color: color ?? this.color,
    );
  }
}

/// Represents a particle effect
class ParticleEffect {
  final double x;
  final double y;
  final Color color;
  final DateTime createdAt;

  const ParticleEffect({
    required this.x,
    required this.y,
    required this.color,
    required this.createdAt,
  });
}

/// Custom painter for the bounce tap game
class BounceTapPainter extends CustomPainter {
  final List<Platform> platforms;
  final List<ParticleEffect> particles;
  final double ballX;
  final double ballY;

  BounceTapPainter({
    required this.platforms,
    required this.particles,
    required this.ballX,
    required this.ballY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw platforms
    for (final platform in platforms) {
      _drawPlatform(canvas, platform);
    }

    // Draw particles
    for (final particle in particles) {
      _drawParticle(canvas, particle);
    }

    // Draw ball
    _drawBall(canvas);
  }

  void _drawPlatform(Canvas canvas, Platform platform) {
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(platform.x, platform.y, platform.width, platform.height),
      const Radius.circular(10),
    );
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = platform.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);
    
    canvas.drawRRect(rect, glowPaint);
    
    // Draw main platform
    final platformPaint = Paint()
      ..color = platform.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(rect, platformPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = platform.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRRect(rect, borderPaint);
  }

  void _drawBall(Canvas canvas) {
    const ballRadius = 15.0;
    final center = Offset(ballX, ballY);
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = const Color(0xFFFF00FF).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);
    
    canvas.drawCircle(center, ballRadius + 6, glowPaint);
    
    // Draw main ball
    final ballPaint = Paint()
      ..color = const Color(0xFFFF00FF).withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, ballRadius, ballPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = const Color(0xFFFF00FF)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(center, ballRadius, borderPaint);
    
    // Draw inner highlight
    final highlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(ballX - 5, ballY - 5),
      4,
      highlightPaint,
    );
  }

  void _drawParticle(Canvas canvas, ParticleEffect particle) {
    final age = DateTime.now().difference(particle.createdAt).inMilliseconds;
    final alpha = (1.0 - (age / 1000.0)).clamp(0.0, 1.0);
    final size = 5 + (age / 50);
    
    final particlePaint = Paint()
      ..color = particle.color.withValues(alpha: alpha * 0.8)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    
    canvas.drawCircle(
      Offset(particle.x, particle.y),
      size,
      particlePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
