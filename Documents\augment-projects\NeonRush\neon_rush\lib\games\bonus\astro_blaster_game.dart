import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';

import '../../core/sound_manager.dart';
import '../../models/neon_theme.dart';

import '../../services/challenges_service.dart';
import '../../models/challenge.dart';

/// Astro Blaster - Space shooter bonus game
class AstroBlasterGame extends StatefulWidget {
  final NeonTheme theme;
  final Function(int score, int tokens)? onGameComplete;

  const AstroBlasterGame({
    super.key,
    required this.theme,
    this.onGameComplete,
  });

  @override
  State<AstroBlasterGame> createState() => _AstroBlasterGameState();
}

class _AstroBlasterGameState extends State<AstroBlasterGame>
    with TickerProviderStateMixin {
  late AnimationController _gameController;
  late AnimationController _explosionController;
  
  // Game state
  bool _isPlaying = false;
  bool _gameOver = false;
  int _score = 0;
  int _lives = 3;
  Duration _timeRemaining = const Duration(seconds: 60);
  Timer? _gameTimer;
  Timer? _spawnTimer;
  
  // Player ship
  Offset _shipPosition = const Offset(0.5, 0.8);
  final double _shipSize = 40.0;
  
  // Game objects
  final List<_Asteroid> _asteroids = [];
  final List<_Bullet> _bullets = [];
  final List<_Explosion> _explosions = [];
  final List<_PowerUp> _powerUps = [];
  
  // Power-ups
  bool _rapidFire = false;
  bool _shield = false;
  Timer? _rapidFireTimer;
  Timer? _shieldTimer;
  
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _gameController = AnimationController(
      duration: const Duration(milliseconds: 16),
      vsync: this,
    );
    _explosionController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _gameController.addListener(_updateGame);
  }

  @override
  void dispose() {
    _gameController.dispose();
    _explosionController.dispose();
    _gameTimer?.cancel();
    _spawnTimer?.cancel();
    _rapidFireTimer?.cancel();
    _shieldTimer?.cancel();
    super.dispose();
  }

  void _startGame() {
    setState(() {
      _isPlaying = true;
      _gameOver = false;
      _score = 0;
      _lives = 3;
      _timeRemaining = const Duration(seconds: 60);
      _shipPosition = const Offset(0.5, 0.8);
      _asteroids.clear();
      _bullets.clear();
      _explosions.clear();
      _powerUps.clear();
      _rapidFire = false;
      _shield = false;
    });

    _gameController.repeat();
    
    // Game timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeRemaining.inSeconds > 0) {
        setState(() {
          _timeRemaining = Duration(seconds: _timeRemaining.inSeconds - 1);
        });
      } else {
        _endGame();
      }
    });
    
    // Spawn timer
    _spawnTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      _spawnAsteroid();
      if (_random.nextDouble() < 0.1) {
        _spawnPowerUp();
      }
    });
  }

  void _endGame() {
    setState(() {
      _isPlaying = false;
      _gameOver = true;
    });
    
    _gameController.stop();
    _gameTimer?.cancel();
    _spawnTimer?.cancel();
    _rapidFireTimer?.cancel();
    _shieldTimer?.cancel();
    
    // Calculate tokens earned
    final tokensEarned = (_score / 100).round().clamp(1, 50);
    
    // Update challenges
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.bonusGames,
      type: ChallengeType.bonusGame,
      amount: 1,
      metadata: {
        'game': 'astro_blaster',
        'score': _score,
      },
    );
    
    widget.onGameComplete?.call(_score, tokensEarned);
    SoundManager().playGameComplete();
  }

  void _updateGame() {
    if (!_isPlaying) return;
    
    setState(() {
      // Update bullets
      _bullets.removeWhere((bullet) {
        bullet.position = Offset(
          bullet.position.dx,
          bullet.position.dy - 0.02,
        );
        return bullet.position.dy < 0;
      });
      
      // Update asteroids
      _asteroids.removeWhere((asteroid) {
        asteroid.position = Offset(
          asteroid.position.dx + asteroid.velocity.dx,
          asteroid.position.dy + asteroid.velocity.dy,
        );
        asteroid.rotation += 0.1;
        return asteroid.position.dy > 1.2;
      });
      
      // Update power-ups
      _powerUps.removeWhere((powerUp) {
        powerUp.position = Offset(
          powerUp.position.dx,
          powerUp.position.dy + 0.01,
        );
        return powerUp.position.dy > 1.2;
      });
      
      // Check collisions
      _checkCollisions();
    });
  }

  void _spawnAsteroid() {
    final size = 0.05 + _random.nextDouble() * 0.05;
    final speed = 0.005 + _random.nextDouble() * 0.01;
    
    _asteroids.add(_Asteroid(
      position: Offset(_random.nextDouble(), -0.1),
      velocity: Offset(
        (_random.nextDouble() - 0.5) * 0.002,
        speed,
      ),
      size: size,
      rotation: 0,
      health: size > 0.08 ? 2 : 1,
    ));
  }

  void _spawnPowerUp() {
    final types = ['rapid_fire', 'shield', 'multi_shot'];
    final type = types[_random.nextInt(types.length)];
    
    _powerUps.add(_PowerUp(
      position: Offset(_random.nextDouble(), -0.1),
      type: type,
    ));
  }

  void _shoot() {
    if (!_isPlaying) return;
    
    SoundManager().playButtonClick();
    
    if (_rapidFire) {
      // Triple shot
      _bullets.add(_Bullet(position: Offset(_shipPosition.dx - 0.02, _shipPosition.dy)));
      _bullets.add(_Bullet(position: Offset(_shipPosition.dx, _shipPosition.dy)));
      _bullets.add(_Bullet(position: Offset(_shipPosition.dx + 0.02, _shipPosition.dy)));
    } else {
      _bullets.add(_Bullet(position: Offset(_shipPosition.dx, _shipPosition.dy)));
    }
  }

  void _checkCollisions() {
    // Bullet-asteroid collisions
    for (int i = _bullets.length - 1; i >= 0; i--) {
      for (int j = _asteroids.length - 1; j >= 0; j--) {
        if (_isColliding(_bullets[i].position, 0.01, _asteroids[j].position, _asteroids[j].size)) {
          _asteroids[j].health--;
          _bullets.removeAt(i);
          
          if (_asteroids[j].health <= 0) {
            _explosions.add(_Explosion(position: _asteroids[j].position));
            _asteroids.removeAt(j);
            _score += 10;
            SoundManager().playLevelComplete();
          }
          break;
        }
      }
    }
    
    // Ship-asteroid collisions
    if (!_shield) {
      for (int i = _asteroids.length - 1; i >= 0; i--) {
        if (_isColliding(_shipPosition, _shipSize / 400, _asteroids[i].position, _asteroids[i].size)) {
          _explosions.add(_Explosion(position: _shipPosition));
          _asteroids.removeAt(i);
          _lives--;
          
          if (_lives <= 0) {
            _endGame();
          } else {
            // Temporary invincibility
            _activateShield(2000);
          }
          break;
        }
      }
    }
    
    // Ship-powerup collisions
    for (int i = _powerUps.length - 1; i >= 0; i--) {
      if (_isColliding(_shipPosition, _shipSize / 400, _powerUps[i].position, 0.03)) {
        _activatePowerUp(_powerUps[i].type);
        _powerUps.removeAt(i);
        SoundManager().playRewardSound();
      }
    }
  }

  bool _isColliding(Offset pos1, double size1, Offset pos2, double size2) {
    final distance = (pos1 - pos2).distance;
    return distance < (size1 + size2);
  }

  void _activatePowerUp(String type) {
    switch (type) {
      case 'rapid_fire':
        _rapidFire = true;
        _rapidFireTimer?.cancel();
        _rapidFireTimer = Timer(const Duration(seconds: 5), () {
          setState(() => _rapidFire = false);
        });
        break;
      case 'shield':
        _activateShield(5000);
        break;
      case 'multi_shot':
        _score += 50;
        break;
    }
  }

  void _activateShield(int milliseconds) {
    _shield = true;
    _shieldTimer?.cancel();
    _shieldTimer = Timer(Duration(milliseconds: milliseconds), () {
      setState(() => _shield = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Starfield background
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  widget.theme.primaryColor.withValues(alpha: 0.1),
                  Colors.black,
                ],
              ),
            ),
          ),
          
          // Game area
          if (_isPlaying) ...[
            // Ship
            Positioned(
              left: MediaQuery.of(context).size.width * _shipPosition.dx - _shipSize / 2,
              top: MediaQuery.of(context).size.height * _shipPosition.dy - _shipSize / 2,
              child: GestureDetector(
                onPanUpdate: (details) {
                  setState(() {
                    _shipPosition = Offset(
                      (_shipPosition.dx + details.delta.dx / MediaQuery.of(context).size.width).clamp(0.05, 0.95),
                      _shipPosition.dy,
                    );
                  });
                },
                child: Container(
                  width: _shipSize,
                  height: _shipSize,
                  decoration: BoxDecoration(
                    color: _shield ? Colors.blue : widget.theme.primaryColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: (_shield ? Colors.blue : widget.theme.primaryColor).withValues(alpha: 0.5),
                        blurRadius: _shield ? 20 : 10,
                        spreadRadius: _shield ? 5 : 2,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.rocket_launch,
                    color: Colors.white,
                    size: _shipSize * 0.6,
                  ),
                ),
              ),
            ),
            
            // Bullets
            ..._bullets.map((bullet) => Positioned(
              left: MediaQuery.of(context).size.width * bullet.position.dx - 2,
              top: MediaQuery.of(context).size.height * bullet.position.dy - 5,
              child: Container(
                width: 4,
                height: 10,
                decoration: BoxDecoration(
                  color: widget.theme.accentColor,
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: widget.theme.accentColor.withValues(alpha: 0.5),
                      blurRadius: 5,
                    ),
                  ],
                ),
              ),
            )),
            
            // Asteroids
            ..._asteroids.map((asteroid) => Positioned(
              left: MediaQuery.of(context).size.width * asteroid.position.dx - MediaQuery.of(context).size.width * asteroid.size / 2,
              top: MediaQuery.of(context).size.height * asteroid.position.dy - MediaQuery.of(context).size.width * asteroid.size / 2,
              child: Transform.rotate(
                angle: asteroid.rotation,
                child: Container(
                  width: MediaQuery.of(context).size.width * asteroid.size,
                  height: MediaQuery.of(context).size.width * asteroid.size,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                ),
              ),
            )),
            
            // Power-ups
            ..._powerUps.map((powerUp) => Positioned(
              left: MediaQuery.of(context).size.width * powerUp.position.dx - 15,
              top: MediaQuery.of(context).size.height * powerUp.position.dy - 15,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: _getPowerUpColor(powerUp.type),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: _getPowerUpColor(powerUp.type).withValues(alpha: 0.5),
                      blurRadius: 10,
                    ),
                  ],
                ),
                child: Icon(
                  _getPowerUpIcon(powerUp.type),
                  color: Colors.white,
                  size: 20,
                ),
              ),
            )),
          ],
          
          // UI
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Score: $_score',
                  style: TextStyle(
                    color: widget.theme.primaryColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Lives: $_lives',
                  style: TextStyle(
                    color: widget.theme.accentColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Time: ${_timeRemaining.inSeconds}s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Shoot button
          if (_isPlaying)
            Positioned(
              bottom: 50,
              right: 30,
              child: GestureDetector(
                onTap: _shoot,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.theme.primaryColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: widget.theme.primaryColor.withValues(alpha: 0.5),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.radio_button_checked,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ),
          
          // Start/Game Over screen
          if (!_isPlaying)
            Container(
              color: Colors.black.withValues(alpha: 0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _gameOver ? 'Game Over!' : 'Astro Blaster',
                      style: TextStyle(
                        color: widget.theme.primaryColor,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_gameOver) ...[
                      const SizedBox(height: 20),
                      Text(
                        'Final Score: $_score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                        ),
                      ),
                    ],
                    const SizedBox(height: 40),
                    ElevatedButton(
                      onPressed: _startGame,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.theme.primaryColor,
                        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                      ),
                      child: Text(
                        _gameOver ? 'Play Again' : 'Start Game',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getPowerUpColor(String type) {
    switch (type) {
      case 'rapid_fire':
        return Colors.red;
      case 'shield':
        return Colors.blue;
      case 'multi_shot':
        return Colors.green;
      default:
        return Colors.yellow;
    }
  }

  IconData _getPowerUpIcon(String type) {
    switch (type) {
      case 'rapid_fire':
        return Icons.speed;
      case 'shield':
        return Icons.shield;
      case 'multi_shot':
        return Icons.scatter_plot;
      default:
        return Icons.star;
    }
  }
}

class _Asteroid {
  Offset position;
  Offset velocity;
  double size;
  double rotation;
  int health;

  _Asteroid({
    required this.position,
    required this.velocity,
    required this.size,
    required this.rotation,
    required this.health,
  });
}

class _Bullet {
  Offset position;

  _Bullet({required this.position});
}

class _Explosion {
  Offset position;

  _Explosion({required this.position});
}

class _PowerUp {
  Offset position;
  String type;

  _PowerUp({
    required this.position,
    required this.type,
  });
}
