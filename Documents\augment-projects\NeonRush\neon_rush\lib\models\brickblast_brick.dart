import 'dart:ui';
import 'dart:math';

/// Represents different types of bricks with varying properties
enum BrickType {
  normal,
  strong,
  reinforced,
  explosive,
  shield,
}

/// Represents a brick in the BrickBlast game
class BrickBlastBrick {
  final String id;
  int hp;
  final int maxHp;
  final BrickType type;
  Offset position;
  final Size size;
  bool isDestroyed;
  bool isAnimating;
  
  // Visual properties
  Color color;
  Color borderColor;
  double glowIntensity;
  double opacity;
  
  // Animation properties
  double crackLevel; // 0.0 to 1.0
  double shakeIntensity;
  
  BrickBlastBrick({
    required this.id,
    required this.hp,
    required this.maxHp,
    required this.type,
    required this.position,
    this.size = const Size(40, 20),
    this.isDestroyed = false,
    this.isAnimating = false,
    Color? color,
    Color? borderColor,
    this.glowIntensity = 1.0,
    this.opacity = 1.0,
    this.crackLevel = 0.0,
    this.shakeIntensity = 0.0,
  }) : color = color ?? _getDefaultColor(type, hp, maxHp),
       borderColor = borderColor ?? _getDefaultBorderColor(type);

  /// Get default color based on brick type and HP
  static Color _getDefaultColor(BrickType type, int hp, int maxHp) {
    final hpRatio = hp / maxHp;
    
    switch (type) {
      case BrickType.normal:
        return Color.lerp(
          const Color(0xFF00FFFF), // Cyan
          const Color(0xFF0080FF), // Blue
          1 - hpRatio,
        )!;
      case BrickType.strong:
        return Color.lerp(
          const Color(0xFF00FF00), // Green
          const Color(0xFF008000), // Dark green
          1 - hpRatio,
        )!;
      case BrickType.reinforced:
        return Color.lerp(
          const Color(0xFFFFFF00), // Yellow
          const Color(0xFFFF8000), // Orange
          1 - hpRatio,
        )!;
      case BrickType.explosive:
        return Color.lerp(
          const Color(0xFFFF0080), // Pink
          const Color(0xFFFF0000), // Red
          1 - hpRatio,
        )!;
      case BrickType.shield:
        return Color.lerp(
          const Color(0xFF8000FF), // Purple
          const Color(0xFF4000FF), // Dark purple
          1 - hpRatio,
        )!;
    }
  }

  /// Get default border color based on brick type
  static Color _getDefaultBorderColor(BrickType type) {
    switch (type) {
      case BrickType.normal:
        return const Color(0xFF80FFFF);
      case BrickType.strong:
        return const Color(0xFF80FF80);
      case BrickType.reinforced:
        return const Color(0xFFFFFF80);
      case BrickType.explosive:
        return const Color(0xFFFF8080);
      case BrickType.shield:
        return const Color(0xFFC080FF);
    }
  }

  /// Create a brick with appropriate HP based on round and type
  factory BrickBlastBrick.forRound({
    required String id,
    required int round,
    required BrickType type,
    required Offset position,
    Size? size,
  }) {
    int baseHp;
    
    switch (type) {
      case BrickType.normal:
        baseHp = 5 + (round * 2);
        break;
      case BrickType.strong:
        baseHp = 10 + (round * 3);
        break;
      case BrickType.reinforced:
        baseHp = 15 + (round * 4);
        break;
      case BrickType.explosive:
        baseHp = 8 + (round * 2);
        break;
      case BrickType.shield:
        baseHp = 20 + (round * 5);
        break;
    }

    return BrickBlastBrick(
      id: id,
      hp: baseHp,
      maxHp: baseHp,
      type: type,
      position: position,
      size: size ?? const Size(40, 20),
    );
  }

  /// Take damage and update visual state
  void takeDamage(int damage) {
    final oldHp = hp;
    hp = (hp - damage).clamp(0, maxHp);
    
    if (hp <= 0) {
      isDestroyed = true;
    } else {
      // Update crack level based on damage taken
      crackLevel = 1.0 - (hp / maxHp);
      
      // Add shake effect when damaged
      shakeIntensity = min(1.0, damage / 10.0);
      
      // Update color based on new HP
      color = _getDefaultColor(type, hp, maxHp);
    }
    
    // Trigger animation if damaged
    if (oldHp != hp) {
      isAnimating = true;
    }
  }

  /// Get token reward for destroying this brick
  int get tokenReward {
    switch (type) {
      case BrickType.normal:
        return 1;
      case BrickType.strong:
        return 2;
      case BrickType.reinforced:
        return 3;
      case BrickType.explosive:
        return 2; // Same as strong but has special effect
      case BrickType.shield:
        return 5;
    }
  }

  /// Check if brick contains a point
  bool containsPoint(Offset point) {
    return point.dx >= position.dx &&
           point.dx <= position.dx + size.width &&
           point.dy >= position.dy &&
           point.dy <= position.dy + size.height;
  }

  /// Get center position of the brick
  Offset get center => Offset(
    position.dx + size.width / 2,
    position.dy + size.height / 2,
  );

  /// Move brick down by specified amount
  void moveDown(double amount) {
    position = Offset(position.dx, position.dy + amount);
  }

  /// Create a copy with modified properties
  BrickBlastBrick copyWith({
    String? id,
    int? hp,
    int? maxHp,
    BrickType? type,
    Offset? position,
    Size? size,
    bool? isDestroyed,
    bool? isAnimating,
    Color? color,
    Color? borderColor,
    double? glowIntensity,
    double? opacity,
    double? crackLevel,
    double? shakeIntensity,
  }) {
    return BrickBlastBrick(
      id: id ?? this.id,
      hp: hp ?? this.hp,
      maxHp: maxHp ?? this.maxHp,
      type: type ?? this.type,
      position: position ?? this.position,
      size: size ?? this.size,
      isDestroyed: isDestroyed ?? this.isDestroyed,
      isAnimating: isAnimating ?? this.isAnimating,
      color: color ?? this.color,
      borderColor: borderColor ?? this.borderColor,
      glowIntensity: glowIntensity ?? this.glowIntensity,
      opacity: opacity ?? this.opacity,
      crackLevel: crackLevel ?? this.crackLevel,
      shakeIntensity: shakeIntensity ?? this.shakeIntensity,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hp': hp,
      'maxHp': maxHp,
      'type': type.index,
      'position': {'x': position.dx, 'y': position.dy},
      'size': {'width': size.width, 'height': size.height},
      'isDestroyed': isDestroyed,
      'isAnimating': isAnimating,
      'color': color.toARGB32(),
      'borderColor': borderColor.toARGB32(),
      'glowIntensity': glowIntensity,
      'opacity': opacity,
      'crackLevel': crackLevel,
      'shakeIntensity': shakeIntensity,
    };
  }

  /// Create from JSON
  factory BrickBlastBrick.fromJson(Map<String, dynamic> json) {
    return BrickBlastBrick(
      id: json['id'],
      hp: json['hp'],
      maxHp: json['maxHp'],
      type: BrickType.values[json['type']],
      position: Offset(json['position']['x'], json['position']['y']),
      size: Size(json['size']['width'], json['size']['height']),
      isDestroyed: json['isDestroyed'],
      isAnimating: json['isAnimating'],
      color: Color(json['color']),
      borderColor: Color(json['borderColor']),
      glowIntensity: json['glowIntensity'],
      opacity: json['opacity'],
      crackLevel: json['crackLevel'],
      shakeIntensity: json['shakeIntensity'],
    );
  }
}
