import 'package:flutter/material.dart';

/// Core constants for the NeonRush token-driven game
class GameConstants {
  // App Info
  static const String gameTitle = 'NeonRush';
  static const String gameVersion = '1.0.0';
  
  // Token Economy
  static const int levelCompletionTokens = 20;
  static const int comboTokensPerStreak = 5; // per 5-tap streak
  static const int objectiveTokensMin = 5;
  static const int objectiveTokensMax = 10;
  static const int dailyChallengeTokens = 15;
  static const int achievementTokensMin = 25;
  static const int achievementTokensMax = 50;
  
  // Mode Costs
  static const int endlessModeCost = 200;
  static const int timeAttackCost = 150;
  static const int puzzleModeCost = 175;
  static const int mirrorModeCost = 160;
  static const int hardcoreModeCost = 250;
  static const int seasonalModeCost = 100;
  static const int bundleDiscount = 15; // 15% off
  static const int rentalCost = 50; // 24-hr rental
  
  // Level Progression
  static const int totalLevels = 100;
  static const int mechanicChangeInterval = 10; // every 10 levels
  static const double initialSpawnInterval = 2.0; // seconds
  static const double spawnIntervalDecay = 0.97; // multiplier per level
  static const int initialTargets = 5;
  static const int targetsPerLevel = 4; // floor(level/4)
  static const int bombsPerLevel = 5; // floor(level/5)
  static const double baseTime = 60.0; // seconds
  static const double timeReductionPerTen = 0.5; // seconds per 10 levels
  
  // Lucky Wheel System
  static const int luckyWheelSpinCost = 50; // Cost per spin after free daily spin
  static const List<int> luckyWheelTokenRewards = [10, 25, 50, 75, 100, 150, 200];
  static const List<String> luckyWheelTokenLabels = ['10', '25', '50', '75', '100', '150', '200'];
  static const List<double> luckyWheelTokenOdds = [0.30, 0.25, 0.20, 0.15, 0.07, 0.025, 0.005]; // Must sum to 1.0

  // Bonus Game Keys
  static const int bonusGameKeyReward = 1; // Number of keys awarded
  static const double bonusGameKeyOdds = 0.05; // 5% chance to win a key
  static const int bonusGameKeyUnlockCost = 250; // Cost per key to unlock bonus games
  static const int bonusGameKeysRequired = 3; // Keys needed to unlock each bonus game

  // UI Layout
  static const double bottomSystemNavPadding = 16.0; // Bottom padding to prevent clipping under system nav buttons
  
  // Animation Durations
  static const Duration buttonPulseDuration = Duration(milliseconds: 1500);
  static const Duration targetSpawnDuration = Duration(milliseconds: 300);
  static const Duration particleLifetime = Duration(milliseconds: 2000);
  static const Duration confettiDuration = Duration(milliseconds: 3000);
  
  // Game Mechanics
  static const double accuracyThreshold = 0.95; // 95% for speed boost
  static const int bombPenaltyThreshold = 2; // pause speed growth
  static const double comboMultiplier = 1.5;
  static const int comboThreshold = 5; // taps for combo
}

/// Neon color palette for the game
class NeonColors {
  static const Color background = Color(0xFF0D0D0D);
  static const Color primaryAccent = Color(0xFF00FFF3);
  static const Color secondaryAccent = Color(0xFFFF00E0);
  static const Color highlights = Color(0xFFFFD700);
  static const Color textPrimary = Color(0xFFE0E0E0);
  static const Color textSecondary = Color(0xFFAAAAAA);
  
  // Additional neon colors for variety
  static const Color electricBlue = Color(0xFF0080FF);
  static const Color neonGreen = Color(0xFF00FF80);
  static const Color neonYellow = Color(0xFFFFFF00);
  static const Color neonRed = Color(0xFFFF0040);
  static const Color hotPink = Color(0xFFFF0080);
  static const Color purpleGlow = Color(0xFF8000FF);
  static const Color orangeFlame = Color(0xFFFF8000);

  // UI colors
  static const Color surface = Color(0xFF1A1A1A);
  static const Color error = Color(0xFFFF4444);
  static const Color success = Color(0xFF00FF80);
  static const Color warning = Color(0xFFFFAA00);
  
  // Gradient combinations
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryAccent, secondaryAccent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFF0D0D0D), Color(0xFF1A1A1A)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}

/// Game modes available in NeonRush
enum GameMode {
  tap,
  swipe,
  hold,
  avoid,
  memory,
  reaction,
  drag,
  rotate,
  deflect,
  chaos,
  // Bonus modes
  endless,
  timeAttack,
  puzzle,
  mirror,
  hardcore,
  seasonal
}

/// Difficulty levels for adaptive gameplay
enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert,
  nightmare
}

/// Game states for state management
enum GameState {
  menu,
  playing,
  paused,
  gameOver,
  levelComplete,
  shop,
  modes,
  settings,
  achievements,
  leaderboard
}

/// Level mechanics that change every 10 levels
enum LevelMechanic {
  basicSpawns,      // 1-10: Basic spawns with bombs at L5
  coloredTargets,   // 11-20: Colored targets
  objectives,       // 21-30: Pop-up objectives
  sizeShifts,       // 31-40: Size variations
  movingTargets,    // 41-50: Moving targets
  mirrorControls,   // 51-60: Mirror/inverted controls
  patternPuzzles,   // 61-70: Pattern-based puzzles
  flickers,         // 71-80: Flickering targets
  twoPhase,         // 81-90: Two-phase gameplay
  highSpeed,        // 91-99: High-speed gauntlet
  bossRun           // 100: Boss run with all modifiers
}

/// Storage keys for SharedPreferences
class StorageKeys {
  static const String playerProfile = 'player_profile';
  static const String tokenBalance = 'token_balance';
  static const String unlockedModes = 'unlocked_modes';
  static const String unlockedBonusGames = 'unlocked_bonus_games';
  static const String dailySpinUsed = 'daily_spin_used';
  static const String loginStreak = 'login_streak';
  static const String lastLoginDate = 'last_login_date';
  static const String achievements = 'achievements';
  static const String settings = 'game_settings';
  static const String statistics = 'game_statistics';
}

/// Achievement types and requirements
class Achievements {
  static const Map<String, Map<String, dynamic>> definitions = {
    'first_level': {
      'name': 'First Steps',
      'description': 'Complete your first level',
      'tokens': 25,
      'requirement': 1,
    },
    'combo_master': {
      'name': 'Combo Master',
      'description': 'Achieve a 10-tap combo',
      'tokens': 50,
      'requirement': 10,
    },
    'speed_demon': {
      'name': 'Speed Demon',
      'description': 'Complete a level in under 30 seconds',
      'tokens': 75,
      'requirement': 30,
    },
    'perfectionist': {
      'name': 'Perfectionist',
      'description': 'Complete a level with 100% accuracy',
      'tokens': 100,
      'requirement': 100,
    },
    'token_collector': {
      'name': 'Token Collector',
      'description': 'Collect 1000 tokens',
      'tokens': 50,
      'requirement': 1000,
    },
    'mode_unlocker': {
      'name': 'Mode Unlocker',
      'description': 'Unlock your first bonus mode',
      'tokens': 75,
      'requirement': 1,
    },
    'streak_keeper': {
      'name': 'Streak Keeper',
      'description': 'Maintain a 7-day login streak',
      'tokens': 100,
      'requirement': 7,
    },
  };
}
