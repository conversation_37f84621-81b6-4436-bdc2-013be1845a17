import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../core/sound_manager.dart';
import '../providers/game_provider.dart';

import '../widgets/token_balance.dart';


import '../ui/neon_text.dart';

import '../ui/dynamic_background.dart';
import '../models/neon_theme.dart';
import 'breakfinity_screen.dart';
import 'brickblast_screen.dart';
import 'bonus_games_screen.dart';
import 'settings_screen.dart';
import 'challenges_screen.dart';
import 'lucky_wheel_screen.dart';
import 'shop_screen.dart';

/// Main home screen of the NeonRush token-driven game
class NeonRushHome extends StatefulWidget {
  const NeonRushHome({super.key});

  @override
  State<NeonRushHome> createState() => _NeonRushHomeState();
}

class _NeonRushHomeState extends State<NeonRushHome> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize the game provider when the home screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<GameProvider>().initialize();
      // Start playing menu music
      SoundManager().playMenuMusic();
      _isInitialized = true;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh token balance when returning from other screens
    if (_isInitialized) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<GameProvider>().refreshTokenBalance();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: DynamicNeonBackground(
        theme: NeonThemes.cyberBlue,
        intensity: 1.2,
        child: Padding(
          padding: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top + core_constants.GameConstants.topSystemPadding,
            bottom: MediaQuery.of(context).padding.bottom + core_constants.GameConstants.bottomSystemNavPadding,
            left: core_constants.GameConstants.sideSystemPadding,
            right: core_constants.GameConstants.sideSystemPadding,
          ),
          child: Consumer<GameProvider>(
            builder: (BuildContext context, GameProvider gameProvider, Widget? child) {
              return Column(
                children: [
                  // Top Bar
                  _buildTopBar(gameProvider),

                  // Main Content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // TapVerse Logo Section
                          _buildLogoSection(),

                          const SizedBox(height: 32),

                          // Core Games Section
                          _buildCoreGamesSection(gameProvider),

                          const SizedBox(height: 32),

                          // Bonus Content Section
                          _buildBonusContentSection(gameProvider),

                          const SizedBox(height: 32),

                          // Progression & Stats Section
                          _buildProgressionSection(gameProvider),

                          // Bottom padding to prevent clipping under system nav buttons
                          SizedBox(height: core_constants.GameConstants.bottomSystemNavPadding),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  // Top Bar with TapVerse logo, tokens, and settings
  Widget _buildTopBar(GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            NeonColors.background,
            NeonColors.background.withValues(alpha: 0.8),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Glowing TapVerse Logo
          NeonText.title(
            core_constants.GameConstants.gameTitle,
            glowColor: NeonColors.primaryAccent,
            textColor: NeonColors.primaryAccent,
            fontSize: 28,
            animate: true,
            glowIntensity: 1.0,
          ),
          // Token Balance, Shopping Cart, and Settings
          Row(
            children: [
              const TokenBalance(),
              const SizedBox(width: 12),
              _buildShoppingCartIcon(),
              const SizedBox(width: 8),
              _buildSettingsIcon(),
            ],
          ),
        ],
      ),
    );
  }

  // Enhanced Logo Section
  Widget _buildLogoSection() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          // Large TapVerse Title
          NeonText(
            core_constants.GameConstants.gameTitle,
            glowColor: NeonColors.primaryAccent,
            textColor: NeonColors.primaryAccent,
            fontSize: 48,
            fontWeight: FontWeight.w900,
            animate: true,
            glowIntensity: 1.2,
          ),
          const SizedBox(height: 16),
          Text(
            '🎮 Tap Your Way to Victory',
            style: GoogleFonts.orbitron(
              color: NeonColors.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.5,
            ),
            textAlign: TextAlign.center,
          ).animate()
              .fadeIn(duration: const Duration(milliseconds: 1000))
              .slideY(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  // Core Games Section - Breakfinity and BrickBlast
  Widget _buildCoreGamesSection(GameProvider gameProvider) {
    return Column(
      children: [
        _buildSectionHeader('🔵 Core Games', NeonColors.primaryAccent),
        const SizedBox(height: 16),
        // Row: Breakfinity and BrickBlast
        Row(
          children: [
            // Breakfinity Game Button
            Expanded(
              child: _buildGameButton(
                title: 'Breakfinity',
                subtitle: 'Tap to Break',
                icon: Icons.grid_4x4,
                color: NeonColors.primaryAccent,
                onTap: () => _navigateToBreakfinity(),
              ),
            ),
            const SizedBox(width: 16),
            // BrickBlast Game Button
            Expanded(
              child: _buildGameButton(
                title: 'BrickBlast',
                subtitle: 'Ball vs Bricks',
                icon: Icons.sports_baseball,
                color: NeonColors.highlights,
                onTap: () => _navigateToBrickBlast(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Bonus Content Section
  Widget _buildBonusContentSection(GameProvider gameProvider) {
    return Column(
      children: [
        _buildSectionHeader('🟣 Bonus Content', NeonColors.secondaryAccent),
        const SizedBox(height: 16),
        Row(
          children: [
            // Bonus Games Button
            Expanded(
              child: _buildGameButton(
                title: 'Bonus Games',
                subtitle: 'Mini Games',
                icon: Icons.stars,
                color: NeonColors.secondaryAccent,
                onTap: () => _navigateToBonusGames(),
              ),
            ),
            const SizedBox(width: 16),
            // Spin Wheel Button
            Expanded(
              child: _buildGameButton(
                title: 'Spin Wheel',
                subtitle: 'Daily Rewards',
                icon: Icons.casino,
                color: NeonColors.highlights,
                onTap: () => _showSpinWheel(gameProvider),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Progression & Stats Section
  Widget _buildProgressionSection(GameProvider gameProvider) {
    return Column(
      children: [
        _buildSectionHeader('🟡 Your Progress', NeonColors.highlights),
        const SizedBox(height: 16),
        Row(
          children: [
            // Challenges Button
            Expanded(
              child: _buildGameButton(
                title: 'Challenges',
                subtitle: 'Daily Tasks',
                icon: Icons.emoji_events,
                color: NeonColors.highlights,
                onTap: () => _navigateToChallenges(),
              ),
            ),
            const SizedBox(width: 16),
            // Statistics Button
            Expanded(
              child: _buildGameButton(
                title: 'Statistics',
                subtitle: 'Your Stats',
                icon: Icons.bar_chart,
                color: NeonColors.neonGreen,
                onTap: () => _showStatistics(gameProvider),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Section Header Widget
  Widget _buildSectionHeader(String title, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            Colors.transparent,
            color.withValues(alpha: 0.1),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        title,
        style: GoogleFonts.orbitron(
          color: color,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          letterSpacing: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
    )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideX(begin: -0.2, end: 0);
  }





  // Game Button Widget with Enhanced Animations
  Widget _buildGameButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final buttonHeight = (constraints.maxWidth * 0.28).clamp(110.0, 150.0);
        final iconSize = (constraints.maxWidth * 0.07).clamp(24.0, 36.0);
        final titleFontSize = (constraints.maxWidth * 0.035).clamp(12.0, 16.0);
        final subtitleFontSize = (constraints.maxWidth * 0.025).clamp(9.0, 12.0);
        final padding = (constraints.maxWidth * 0.025).clamp(10.0, 16.0);

        return GestureDetector(
          onTap: () async {
            await SoundManager().playSfx(SoundType.buttonClick);
            onTap();
          },
          child: Container(
            height: buttonHeight,
            padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.2),
              color.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 12,
              spreadRadius: 2,
            ),
            // Additional inner glow
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 6,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: iconSize,
            )
                .animate(onPlay: (controller) => controller.repeat())
                .shimmer(
                  duration: const Duration(seconds: 3),
                  color: color.withValues(alpha: 0.3),
                ),
            SizedBox(height: padding * 0.4),
            Text(
              title,
              style: GoogleFonts.orbitron(
                color: color,
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.2,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: GoogleFonts.orbitron(
                color: color.withValues(alpha: 0.8),
                fontSize: subtitleFontSize,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 400))
        .scale(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOut,
        )
        .then()
        .shimmer(
          duration: const Duration(seconds: 2),
          color: color.withValues(alpha: 0.1),
        );
      },
    );
  }

  // Shopping Cart Icon
  Widget _buildShoppingCartIcon() {
    return GestureDetector(
      onTap: () async {
        await SoundManager().playSfx(SoundType.buttonClick);
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const ShopScreen(),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: NeonColors.secondaryAccent.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: NeonColors.secondaryAccent.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: NeonColors.secondaryAccent.withValues(alpha: 0.2),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Icon(
          Icons.shopping_cart,
          color: NeonColors.secondaryAccent,
          size: 24,
        ),
      ),
    );
  }

  // Settings Icon
  Widget _buildSettingsIcon() {
    return GestureDetector(
      onTap: () async {
        await SoundManager().playSfx(SoundType.buttonClick);
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SettingsScreen(),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: NeonColors.primaryAccent.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: NeonColors.primaryAccent.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: NeonColors.primaryAccent.withValues(alpha: 0.2),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Icon(
          Icons.settings,
          color: NeonColors.primaryAccent,
          size: 24,
        ),
      ),
    );
  }

  // Action handlers


  void _showSpinWheel(GameProvider gameProvider) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LuckyWheelScreen(),
      ),
    );
  }



  void _navigateToBonusGames() {
    SoundManager().playButtonClick();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BonusGamesScreen(),
      ),
    );
  }

  void _navigateToChallenges() {
    SoundManager().playButtonClick();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChallengesScreen(),
      ),
    );
  }

  void _navigateToBreakfinity() {
    SoundManager().playButtonClick();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BreakfinityScreen(),
      ),
    );
  }

  void _navigateToBrickBlast() {
    SoundManager().playButtonClick();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BrickBlastScreen(),
      ),
    );
  }





  void _showStatistics(GameProvider gameProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'Statistics',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: double.maxFinite,
            maxHeight: MediaQuery.of(context).size.height * 0.5,
          ),
          child: ListView.builder(
            itemCount: gameProvider.statistics.length,
            itemBuilder: (context, index) {
              final stat = gameProvider.statistics.entries.elementAt(index);
              return LayoutBuilder(
                builder: (context, constraints) {
                  final titleFontSize = (constraints.maxWidth * 0.035).clamp(12.0, 16.0);
                  final valueFontSize = (constraints.maxWidth * 0.04).clamp(14.0, 18.0);

                  return ListTile(
                    title: Text(
                      stat.key.replaceAll('_', ' ').toUpperCase(),
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textPrimary,
                        fontWeight: FontWeight.bold,
                        fontSize: titleFontSize,
                      ),
                    ),
                    trailing: Text(
                      stat.value.toString(),
                      style: GoogleFonts.orbitron(
                        color: NeonColors.highlights,
                        fontSize: valueFontSize,
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
            ),
          ),
        ],
      ),
    );
  }


}
