import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/game_provider.dart';
import '../../core/sound_manager.dart';
import '../../models/token_transaction.dart';

/// TapShot - Tap lanes to shoot falling targets
class TapShotGame extends StatefulWidget {
  final Function(int score, int tokens)? onGameComplete;

  const TapShotGame({
    super.key,
    this.onGameComplete,
  });

  @override
  State<TapShotGame> createState() => _TapShotGameState();
}

class _TapShotGameState extends State<TapShotGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _spawnTimer;
  
  final List<FallingTarget> _targets = [];
  final List<Bullet> _bullets = [];
  final List<Explosion> _explosions = [];
  int _score = 0;
  int _lives = 3;
  bool _gameActive = false;
  double _spawnRate = 2.0; // targets per second
  
  static const int laneCount = 4;
  static const double targetSpeed = 100.0; // pixels per second
  static const double bulletSpeed = 300.0; // pixels per second
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _spawnTimer.cancel();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _targets.clear();
      _bullets.clear();
      _explosions.clear();
      _score = 0;
      _lives = 3;
      _gameActive = true;
      _spawnRate = 2.0;
    });

    _startGameTimer();
    _startSpawnTimer();
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) return;
      
      _updateGame();
    });
  }

  void _startSpawnTimer() {
    _spawnTimer = Timer.periodic(Duration(milliseconds: (1000 / _spawnRate).round()), (timer) {
      if (!_gameActive) return;
      
      _spawnTarget();
    });
  }

  void _updateGame() {
    final screenHeight = MediaQuery.of(context).size.height;
    
    setState(() {
      // Update targets
      for (int i = _targets.length - 1; i >= 0; i--) {
        _targets[i].y += targetSpeed * 0.016; // 16ms frame time
        
        // Remove targets that reached bottom
        if (_targets[i].y > screenHeight) {
          _targets.removeAt(i);
          _loseLife();
        }
      }
      
      // Update bullets
      for (int i = _bullets.length - 1; i >= 0; i--) {
        _bullets[i].y -= bulletSpeed * 0.016;
        
        // Remove bullets that went off screen
        if (_bullets[i].y < 0) {
          _bullets.removeAt(i);
          continue;
        }
        
        // Check bullet-target collisions
        for (int j = _targets.length - 1; j >= 0; j--) {
          if (_checkCollision(_bullets[i], _targets[j])) {
            _hitTarget(_targets[j], j);
            _bullets.removeAt(i);
            break;
          }
        }
      }
      
      // Update explosions
      _explosions.removeWhere((explosion) {
        return DateTime.now().difference(explosion.createdAt).inMilliseconds > 500;
      });
    });
  }

  void _spawnTarget() {
    final random = Random();
    final screenWidth = MediaQuery.of(context).size.width;
    final laneWidth = screenWidth / laneCount;
    final lane = random.nextInt(laneCount);
    
    final isSpecial = random.nextDouble() < 0.1; // 10% chance for special target
    
    final target = FallingTarget(
      x: lane * laneWidth + laneWidth / 2,
      y: 0,
      lane: lane,
      color: isSpecial ? const Color(0xFFFFD700) : const Color(0xFF00FFFF),
      points: isSpecial ? 50 : 10,
      isSpecial: isSpecial,
    );
    
    setState(() {
      _targets.add(target);
    });
  }

  bool _checkCollision(Bullet bullet, FallingTarget target) {
    final distance = sqrt(pow(bullet.x - target.x, 2) + pow(bullet.y - target.y, 2));
    return distance < 30; // Collision radius
  }

  void _hitTarget(FallingTarget target, int targetIndex) async {
    setState(() {
      _targets.removeAt(targetIndex);
      _score += target.points;
      
      // Add explosion effect
      _explosions.add(Explosion(
        x: target.x,
        y: target.y,
        color: target.color,
        createdAt: DateTime.now(),
      ));
    });

    SoundManager().playSfx(SoundType.buttonClick);
  }

  void _loseLife() async {
    setState(() {
      _lives--;
    });
    
    if (_lives <= 0) {
      _endGame();
    }
  }

  void _shootBullet(int lane) async {
    if (!_gameActive) return;
    
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final laneWidth = screenWidth / laneCount;
    
    final bullet = Bullet(
      x: lane * laneWidth + laneWidth / 2,
      y: screenHeight - 100,
      lane: lane,
      color: const Color(0xFF00FFFF),
    );
    
    setState(() {
      _bullets.add(bullet);
    });
    
    SoundManager().playSfx(SoundType.buttonClick);
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final tokens = _score ~/ 5; // 1 token per 5 points
    
    await gameProvider.earnTokens(
      tokens,
      TokenTransactionType.bonusGameReward,
      'TapShot game completed',
    );

    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, tokens);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final laneWidth = screenWidth / laneCount;

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Lane dividers
            for (int i = 1; i < laneCount; i++)
              Positioned(
                left: i * laneWidth,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 1,
                  color: Colors.white.withValues(alpha: 0.3),
                ),
              ),
            
            // Targets
            for (final target in _targets)
              Positioned(
                left: target.x - 15,
                top: target.y - 15,
                child: Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: target.color,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: target.color.withValues(alpha: 0.5),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Bullets
            for (final bullet in _bullets)
              Positioned(
                left: bullet.x - 5,
                top: bullet.y - 10,
                child: Container(
                  width: 10,
                  height: 20,
                  decoration: BoxDecoration(
                    color: bullet.color,
                    borderRadius: BorderRadius.circular(5),
                    boxShadow: [
                      BoxShadow(
                        color: bullet.color.withValues(alpha: 0.5),
                        blurRadius: 5,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Explosions
            for (final explosion in _explosions)
              Positioned(
                left: explosion.x - 25,
                top: explosion.y - 25,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: explosion.color.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: explosion.color.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Shooting lanes
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: 100,
              child: Row(
                children: List.generate(laneCount, (index) {
                  return Expanded(
                    child: GestureDetector(
                      onTap: () => _shootBullet(index),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                          color: Colors.white.withValues(alpha: 0.1),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.keyboard_arrow_up,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
            
            // UI
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Score: $_score',
                    style: const TextStyle(color: Colors.white, fontSize: 18),
                  ),
                  Row(
                    children: List.generate(_lives, (index) {
                      return const Icon(
                        Icons.favorite,
                        color: Colors.red,
                        size: 24,
                      );
                    }),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FallingTarget {
  double x;
  double y;
  final int lane;
  final Color color;
  final int points;
  final bool isSpecial;

  FallingTarget({
    required this.x,
    required this.y,
    required this.lane,
    required this.color,
    required this.points,
    this.isSpecial = false,
  });
}

class Bullet {
  double x;
  double y;
  final int lane;
  final Color color;

  Bullet({
    required this.x,
    required this.y,
    required this.lane,
    required this.color,
  });
}

class Explosion {
  final double x;
  final double y;
  final Color color;
  final DateTime createdAt;

  Explosion({
    required this.x,
    required this.y,
    required this.color,
    required this.createdAt,
  });
}
