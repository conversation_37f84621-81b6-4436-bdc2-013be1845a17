import 'package:flutter/material.dart';
import 'package:flame/components.dart';
import 'package:flame/particles.dart';
import 'package:flame/game.dart';
import 'dart:math';

/// Flame-based particle system for enhanced visual effects
class FlameParticleWidget extends StatefulWidget {
  final Size size;
  final List<FlameParticleEffect> effects;

  const FlameParticleWidget({
    super.key,
    required this.size,
    required this.effects,
  });

  @override
  State<FlameParticleWidget> createState() => _FlameParticleWidgetState();
}

class _FlameParticleWidgetState extends State<FlameParticleWidget> {
  late FlameParticleGame _game;

  @override
  void initState() {
    super.initState();
    _game = FlameParticleGame();
  }

  @override
  void didUpdateWidget(FlameParticleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Add new effects to the game
    for (final effect in widget.effects) {
      if (!oldWidget.effects.contains(effect)) {
        _game.addParticleEffect(effect);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size.width,
      height: widget.size.height,
      child: GameWidget(game: _game),
    );
  }
}

/// Flame game component for managing particles
class FlameParticleGame extends FlameGame {
  final List<ParticleSystemComponent> _activeParticles = [];

  @override
  Future<void> onLoad() async {
    super.onLoad();
  }

  void addParticleEffect(FlameParticleEffect effect) {
    final particleComponent = _createParticleComponent(effect);
    add(particleComponent);
    _activeParticles.add(particleComponent);
    
    // Remove particle after its lifetime
    Future.delayed(Duration(milliseconds: (effect.lifetime * 1000).round()), () {
      if (_activeParticles.contains(particleComponent)) {
        remove(particleComponent);
        _activeParticles.remove(particleComponent);
      }
    });
  }

  ParticleSystemComponent _createParticleComponent(FlameParticleEffect effect) {
    switch (effect.type) {
      case FlameParticleType.tap:
        return _createTapParticles(effect);
      case FlameParticleType.breakEffect:
        return _createBreakParticles(effect);
      case FlameParticleType.explosion:
        return _createExplosionParticles(effect);
    }
  }

  ParticleSystemComponent _createTapParticles(FlameParticleEffect effect) {
    return ParticleSystemComponent(
      particle: Particle.generate(
        count: 15,
        lifespan: effect.lifetime,
        generator: (i) {
          final angle = (i / 15.0) * 2 * pi;
          final speed = 50.0 + Random().nextDouble() * 100.0;
          
          return AcceleratedParticle(
            acceleration: Vector2(0, 200), // Gravity
            speed: Vector2(cos(angle) * speed, sin(angle) * speed),
            position: Vector2(effect.position.dx, effect.position.dy),
            child: CircleParticle(
              radius: 2.0 + Random().nextDouble() * 3.0,
              paint: Paint()
                ..color = effect.color
                ..style = PaintingStyle.fill,
            ),
          );
        },
      ),
    );
  }

  ParticleSystemComponent _createBreakParticles(FlameParticleEffect effect) {
    return ParticleSystemComponent(
      particle: ComposedParticle(
        children: [
          // Layer 1: Central explosion burst
          Particle.generate(
            count: 15,
            lifespan: effect.lifetime * 0.8,
            generator: (i) {
              final angle = (i / 15.0) * 2 * pi;
              final speed = 150.0 + Random().nextDouble() * 100.0;

              return AcceleratedParticle(
                acceleration: Vector2(0, 400),
                speed: Vector2(cos(angle) * speed, sin(angle) * speed),
                position: Vector2(effect.position.dx, effect.position.dy),
                child: ScalingParticle(
                  lifespan: effect.lifetime * 0.8,
                  child: ComposedParticle(
                    children: [
                      // Core particle
                      CircleParticle(
                        radius: 4.0 + Random().nextDouble() * 3.0,
                        paint: Paint()
                          ..color = effect.color
                          ..style = PaintingStyle.fill,
                      ),
                      // Bright glow
                      CircleParticle(
                        radius: 8.0 + Random().nextDouble() * 6.0,
                        paint: Paint()
                          ..color = effect.color.withValues(alpha: 0.6)
                          ..style = PaintingStyle.fill
                          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Layer 2: Scattered fragments
          Particle.generate(
            count: 20,
            lifespan: effect.lifetime * 1.2,
            generator: (i) {
              final angle = Random().nextDouble() * 2 * pi;
              final speed = 80.0 + Random().nextDouble() * 120.0;

              return AcceleratedParticle(
                acceleration: Vector2(0, 300),
                speed: Vector2(cos(angle) * speed, sin(angle) * speed),
                position: Vector2(effect.position.dx, effect.position.dy),
                child: ScalingParticle(
                  lifespan: effect.lifetime * 1.2,
                  child: CircleParticle(
                    radius: 2.0 + Random().nextDouble() * 2.0,
                    paint: Paint()
                      ..color = effect.color.withValues(alpha: 0.8)
                      ..style = PaintingStyle.fill,
                  ),
                ),
              );
            },
          ),

          // Layer 3: Shockwave ring
          ScalingParticle(
            lifespan: effect.lifetime * 0.6,
            child: CircleParticle(
              radius: 20.0,
              paint: Paint()
                ..color = effect.color.withValues(alpha: 0.2)
                ..style = PaintingStyle.stroke
                ..strokeWidth = 3.0
                ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0),
            ),
          ),
        ],
      ),
    );
  }

  ParticleSystemComponent _createExplosionParticles(FlameParticleEffect effect) {
    return ParticleSystemComponent(
      particle: Particle.generate(
        count: 40,
        lifespan: effect.lifetime,
        generator: (i) {
          final angle = Random().nextDouble() * 2 * pi;
          final speed = 150.0 + Random().nextDouble() * 200.0;
          
          return AcceleratedParticle(
            acceleration: Vector2(0, 400), // Strong gravity
            speed: Vector2(cos(angle) * speed, sin(angle) * speed),
            position: Vector2(effect.position.dx, effect.position.dy),
            child: ComposedParticle(
              children: [
                // Core explosion particle
                CircleParticle(
                  radius: 4.0 + Random().nextDouble() * 6.0,
                  paint: Paint()
                    ..color = effect.color
                    ..style = PaintingStyle.fill,
                ),
                // Outer glow
                CircleParticle(
                  radius: 10.0 + Random().nextDouble() * 15.0,
                  paint: Paint()
                    ..color = effect.color.withValues(alpha: 0.2)
                    ..style = PaintingStyle.fill
                    ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0),
                ),
                // Spark trails (using CircleParticle instead of RectangleParticle)
                CircleParticle(
                  radius: 1.0 + Random().nextDouble() * 2.0,
                  paint: Paint()
                    ..color = effect.color.withValues(alpha: 0.8)
                    ..style = PaintingStyle.fill,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Represents a particle effect to be created
class FlameParticleEffect {
  final FlameParticleType type;
  final Offset position;
  final Color color;
  final double lifetime;

  const FlameParticleEffect({
    required this.type,
    required this.position,
    required this.color,
    this.lifetime = 1.0,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FlameParticleEffect &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          position == other.position &&
          color == other.color &&
          lifetime == other.lifetime;

  @override
  int get hashCode =>
      type.hashCode ^ position.hashCode ^ color.hashCode ^ lifetime.hashCode;
}

/// Types of particle effects
enum FlameParticleType {
  tap,
  breakEffect,
  explosion,
}
