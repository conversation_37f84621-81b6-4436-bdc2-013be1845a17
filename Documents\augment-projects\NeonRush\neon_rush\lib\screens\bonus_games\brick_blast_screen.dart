import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants.dart';
import '../../models/brickblast_ball.dart';
import '../../providers/game_provider.dart';
import '../../services/token_service.dart';
import '../../models/token_transaction.dart';

enum BrickType {
  normal,
  reinforced,
  armored,
  explosive,
  regenerating,
}

class Brick {
  double x;
  double y;
  double width;
  double height;
  int hp;
  int maxHp;
  Color color;
  BrickType type;
  DateTime? lastRegenTime;
  bool isExploding;

  Brick({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.hp,
    required this.maxHp,
    required this.color,
    this.type = BrickType.normal,
    this.lastRegenTime,
    this.isExploding = false,
  });
}

extension OffsetExtension on Offset {
  Offset get normalized {
    final magnitude = distance;
    if (magnitude == 0) return Offset.zero;
    return this / magnitude;
  }
}

class BrickBlastScreen extends StatefulWidget {
  const BrickBlastScreen({super.key});

  @override
  State<BrickBlastScreen> createState() => _BrickBlastScreenState();
}

class _BrickBlastScreenState extends State<BrickBlastScreen> {
  static const double gameWidth = 400.0;
  static const double gameHeight = 600.0;
  static const double wallThickness = 10.0;
  static const double brickWidth = 40.0;
  static const double brickHeight = 20.0;
  static const int bricksPerRow = 8;
  static const int baseBrickRows = 6; // Starting rows
  static const int maxBrickRows = 25; // Maximum rows for higher levels
  
  // Game state
  Timer? gameTimer;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  int highScore = 0;
  int round = 1;
  
  // Ball state
  List<BrickBlastBall> balls = [];
  bool isAiming = false;
  Offset? aimDirection;
  
  // Bricks
  List<Brick> bricks = [];
  int ballStock = 10; // Current ball stock
  int maxBallStock = 10; // Maximum ball stock
  DateTime? lastBallRestock; // Last time a ball was restocked
  Timer? ballRestockTimer; // Timer for ball restocking
  int ballHpUpgrade = 0; // Ball HP upgrade level

  // Game objects
  final Random random = Random();
  
  @override
  void initState() {
    super.initState();
    _loadHighScore();
    _loadGameState();
    _initializeBallStock();
    _startBallRestockTimer();
  }

  @override
  void dispose() {
    _saveGameState();
    gameTimer?.cancel();
    ballRestockTimer?.cancel();
    super.dispose();
  }

  void _loadHighScore() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    highScore = gameProvider.getBonusGameHighScore('brick_blast');
  }

  Future<void> _saveGameState() async {
    final prefs = await SharedPreferences.getInstance();

    // Save game progress and upgrades
    await prefs.setInt('brickblast_level', round);
    await prefs.setInt('brickblast_score', score);
    await prefs.setInt('brickblast_ball_hp_upgrade', ballHpUpgrade);
  }

  Future<void> _loadGameState() async {
    final prefs = await SharedPreferences.getInstance();

    // Load game progress and upgrades
    round = prefs.getInt('brickblast_level') ?? 1;
    score = prefs.getInt('brickblast_score') ?? 0;
    ballHpUpgrade = prefs.getInt('brickblast_ball_hp_upgrade') ?? 0;

    // Always generate fresh bricks for now
    _generateBricks();

    // Always regenerate balls when returning to game
    balls.clear();
    if (ballStock > 0) {
      _spawnBall();
    }
  }

  void _initializeBallStock() {
    // Load ball stock from storage or set default
    ballStock = 10;
    maxBallStock = 10;
    lastBallRestock = DateTime.now();
  }

  void _startBallRestockTimer() {
    ballRestockTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkBallRestock();
    });
  }

  void _checkBallRestock() {
    if (ballStock >= maxBallStock) return;

    final now = DateTime.now();
    final timeSinceLastRestock = now.difference(lastBallRestock ?? now);

    // Restock 1 ball every 30 minutes
    if (timeSinceLastRestock.inMinutes >= 30) {
      setState(() {
        ballStock = (ballStock + 1).clamp(0, maxBallStock);
        lastBallRestock = now;
      });
    }
  }

  void _generateBricks() {
    bricks.clear();

    // Progressive difficulty: more rows and harder bricks as level increases
    final currentRows = (baseBrickRows + (round ~/ 2)).clamp(baseBrickRows, maxBrickRows);

    for (int row = 0; row < currentRows; row++) {
      for (int col = 0; col < bricksPerRow; col++) {
        final x = wallThickness + (col * (brickWidth + 2));
        final y = 50 + (row * (brickHeight + 2));

        // Determine brick type based on round and position
        final brickType = _determineBrickType(row, col, round);
        final hp = _getBrickHP(brickType, round);

        bricks.add(Brick(
          x: x,
          y: y,
          width: brickWidth,
          height: brickHeight,
          hp: hp,
          maxHp: hp,
          color: _getBrickColor(hp, brickType),
          type: brickType,
          lastRegenTime: brickType == BrickType.regenerating ? DateTime.now() : null,
        ));
      }
    }
  }

  BrickType _determineBrickType(int row, int col, int round) {
    // Progressive special brick chance for 1000 levels
    final baseSpecialChance = (round * 0.05).clamp(0.0, 0.8);
    final highLevelBonus = round > 100 ? (round - 100) * 0.001 : 0.0;
    final specialChance = (baseSpecialChance + highLevelBonus).clamp(0.0, 0.9);

    if (random.nextDouble() < specialChance) {
      // Back rows more likely to have special bricks
      final backRowBonus = row < 3 ? 0.3 : 0.0;
      final totalChance = specialChance + backRowBonus;

      // Higher level = more dangerous brick types
      if (round > 200 && random.nextDouble() < totalChance * 0.15) {
        return BrickType.regenerating; // More regenerating at high levels
      } else if (round > 50 && random.nextDouble() < totalChance * 0.25) {
        return BrickType.explosive;
      } else if (round > 20 && random.nextDouble() < totalChance * 0.35) {
        return BrickType.armored;
      } else if (random.nextDouble() < totalChance * 0.45) {
        return BrickType.reinforced;
      }
    }

    return BrickType.normal;
  }

  int _getBrickHP(BrickType type, int round) {
    // Progressive HP scaling for 1000 levels
    final baseMultiplier = 1 + (round ~/ 10); // Increases every 10 levels
    final levelBonus = (round ~/ 50); // Extra bonus every 50 levels

    switch (type) {
      case BrickType.normal:
        return (1 + baseMultiplier + random.nextInt(2) + levelBonus).clamp(1, 50);
      case BrickType.reinforced:
        return (3 + baseMultiplier * 2 + random.nextInt(3) + levelBonus).clamp(3, 80);
      case BrickType.armored:
        return (5 + baseMultiplier * 3 + random.nextInt(4) + levelBonus * 2).clamp(5, 120);
      case BrickType.explosive:
        return (2 + baseMultiplier + random.nextInt(2) + levelBonus).clamp(2, 40);
      case BrickType.regenerating:
        return (2 + baseMultiplier + random.nextInt(2) + levelBonus).clamp(2, 60);
    }
  }

  Color _getBrickColor(int hp, [BrickType? type]) {
    // Special colors for special brick types
    switch (type) {
      case BrickType.explosive:
        return const Color(0xFFFF4444); // Bright red
      case BrickType.armored:
        return const Color(0xFF888888); // Gray
      case BrickType.reinforced:
        return const Color(0xFF4444FF); // Blue
      case BrickType.regenerating:
        return const Color(0xFF44FF44); // Bright green
      case BrickType.normal:
      case null:
        // Standard HP-based colors
        if (hp <= 2) return const Color(0xFF00FFFF); // Cyan
        if (hp <= 4) return const Color(0xFF00FF00); // Green
        if (hp <= 6) return const Color(0xFFFFFF00); // Yellow
        if (hp <= 8) return const Color(0xFFFF8000); // Orange
        return const Color(0xFFFF0000); // Red
    }
  }

  void _startGame() {
    setState(() {
      isGameRunning = true;
      isGameOver = false;
      balls.clear();
      isAiming = false;
      aimDirection = null;
    });

    // Spawn ball if we have stock
    if (ballStock > 0) {
      _spawnBall();
    }

    gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      _updateGame();
    });
  }



  void _spawnBall() {
    if (ballStock <= 0) return; // No balls available

    final ballHp = 10 + (ballHpUpgrade * 5); // Base 10 HP + 5 per upgrade level
    final ball = BrickBlastBall(
      id: 'ball_${DateTime.now().millisecondsSinceEpoch}',
      hp: ballHp,
      maxHp: ballHp,
      position: Offset(gameWidth / 2, gameHeight - 50),
      radius: 8.0,
    );

    setState(() {
      balls.add(ball);
      ballStock--; // Consume a ball from stock
    });
  }

  void _updateGame() {
    if (!isGameRunning || isGameOver) return;

    setState(() {
      // Update balls
      for (int i = balls.length - 1; i >= 0; i--) {
        final ball = balls[i];
        if (!ball.isLaunched) continue;

        // Update position
        ball.updatePosition(1.0 / 60.0); // 60 FPS

        // Wall collisions
        _handleWallCollisions(ball);
        
        // Brick collisions
        _handleBrickCollisions(ball);
        
        // Safety check: remove balls that somehow escape the game area
        if (ball.position.dx < -20 || ball.position.dx > gameWidth + 20 ||
            ball.position.dy < -20 || ball.position.dy > gameHeight + 20) {
          balls.removeAt(i);
          continue;
        }

        // Remove depleted balls
        if (ball.isDepleted) {
          balls.removeAt(i);
        }
      }

      // Check win condition
      if (bricks.isEmpty) {
        // Respawn bricks in new pattern instead of just advancing round
        _respawnBricks();
      }

      // Check lose condition (no balls left and no stock)
      if (balls.isEmpty && !isAiming && ballStock <= 0) {
        _gameOver();
      }
    });
  }

  void _handleWallCollisions(BrickBlastBall ball) {
    // Left wall - speed boost
    if (ball.position.dx - ball.radius <= wallThickness) {
      ball.position = Offset(wallThickness + ball.radius, ball.position.dy);
      ball.velocity = Offset(-ball.velocity.dx.abs() * 1.2, ball.velocity.dy); // Ensure positive X velocity
    }

    // Right wall - speed boost
    if (ball.position.dx + ball.radius >= gameWidth - wallThickness) {
      ball.position = Offset(gameWidth - wallThickness - ball.radius, ball.position.dy);
      ball.velocity = Offset(-ball.velocity.dx.abs() * 1.2, ball.velocity.dy); // Ensure negative X velocity
    }

    // Top wall - bounce back down (WALL JUST ABOVE BRICKS)
    if (ball.position.dy - ball.radius <= 45) { // Wall just above the bricks (bricks start at y=50)
      ball.position = Offset(ball.position.dx, 45 + ball.radius);
      ball.velocity = Offset(ball.velocity.dx, ball.velocity.dy.abs() * 1.1); // Ensure positive Y velocity (downward)
      HapticFeedback.lightImpact();
    }

    // Bottom wall - bounce up
    if (ball.position.dy + ball.radius >= gameHeight - wallThickness) {
      ball.position = Offset(ball.position.dx, gameHeight - wallThickness - ball.radius);
      ball.velocity = Offset(ball.velocity.dx, -ball.velocity.dy.abs()); // Ensure negative Y velocity (upward)
    }
  }

  void _handleBrickCollisions(BrickBlastBall ball) {
    for (int i = bricks.length - 1; i >= 0; i--) {
      final brick = bricks[i];

      if (_checkBallBrickCollision(ball, brick)) {
        // Handle special brick effects
        _handleSpecialBrickEffects(brick, ball);

        // Damage brick (armored bricks take less damage)
        final damage = brick.type == BrickType.armored ? 1 : 2;
        brick.hp -= damage;
        ball.takeDamage(1);

        // Award tokens based on brick type
        final tokenReward = _getBrickTokenReward(brick.type);
        score += tokenReward;
        TokenService.earnTokens(
          amount: tokenReward,
          type: TokenTransactionType.levelCompletion,
          description: 'BrickBlast ${brick.type.name} brick hit',
        );

        // Check for brick destruction
        if (brick.hp <= 0) {
          _destroyBrick(i, brick);
        }

        // Simple bounce (reverse Y velocity)
        ball.velocity = Offset(ball.velocity.dx, -ball.velocity.dy);

        HapticFeedback.lightImpact();
        break;
      }
    }
  }

  void _handleSpecialBrickEffects(Brick brick, BrickBlastBall ball) {
    switch (brick.type) {
      case BrickType.explosive:
        if (brick.hp <= 1) {
          // Explosive bricks damage nearby bricks when destroyed
          _explodeBrick(brick);
        }
        break;
      case BrickType.regenerating:
        // Regenerating bricks slowly heal over time
        final now = DateTime.now();
        if (brick.lastRegenTime != null &&
            now.difference(brick.lastRegenTime!).inSeconds >= 10) {
          brick.hp = (brick.hp + 1).clamp(0, brick.maxHp);
          brick.lastRegenTime = now;
        }
        break;
      default:
        break;
    }
  }

  int _getBrickTokenReward(BrickType type) {
    switch (type) {
      case BrickType.normal:
        return 1;
      case BrickType.reinforced:
        return 2;
      case BrickType.armored:
        return 3;
      case BrickType.explosive:
        return 4;
      case BrickType.regenerating:
        return 3;
    }
  }

  void _destroyBrick(int index, Brick brick) {
    bricks.removeAt(index);
    score += _getBrickDestroyBonus(brick.type);
    TokenService.earnTokens(
      amount: _getBrickDestroyBonus(brick.type),
      type: TokenTransactionType.levelCompletion,
      description: 'BrickBlast ${brick.type.name} brick destroyed',
    );
  }

  int _getBrickDestroyBonus(BrickType type) {
    switch (type) {
      case BrickType.normal:
        return 5;
      case BrickType.reinforced:
        return 8;
      case BrickType.armored:
        return 12;
      case BrickType.explosive:
        return 15;
      case BrickType.regenerating:
        return 10;
    }
  }

  void _explodeBrick(Brick explosiveBrick) {
    // Find nearby bricks within explosion radius
    const explosionRadius = 60.0;
    final explosionCenter = Offset(
      explosiveBrick.x + explosiveBrick.width / 2,
      explosiveBrick.y + explosiveBrick.height / 2,
    );

    for (int i = bricks.length - 1; i >= 0; i--) {
      final brick = bricks[i];
      final brickCenter = Offset(
        brick.x + brick.width / 2,
        brick.y + brick.height / 2,
      );

      final distance = (explosionCenter - brickCenter).distance;
      if (distance <= explosionRadius && brick != explosiveBrick) {
        brick.hp -= 2; // Explosion damage
        if (brick.hp <= 0) {
          _destroyBrick(i, brick);
        }
      }
    }
  }

  bool _checkBallBrickCollision(BrickBlastBall ball, Brick brick) {
    final ballLeft = ball.position.dx - ball.radius;
    final ballRight = ball.position.dx + ball.radius;
    final ballTop = ball.position.dy - ball.radius;
    final ballBottom = ball.position.dy + ball.radius;
    
    return ballRight > brick.x &&
           ballLeft < brick.x + brick.width &&
           ballBottom > brick.y &&
           ballTop < brick.y + brick.height;
  }



  void _respawnBricks() {
    // Advance to next level
    round++;

    // Respawn bricks in a different pattern
    _generateBricksWithPattern();

    // Award bonus for surviving long enough to see respawn
    TokenService.earnTokens(
      amount: 20,
      type: TokenTransactionType.levelCompletion,
      description: 'BrickBlast level $round completed',
    );

    // Save progress
    _saveGameState();
  }

  void _generateBricksWithPattern() {
    bricks.clear();

    // Create different patterns based on round
    final patternType = round % 4;
    final currentRows = (baseBrickRows + (round ~/ 2)).clamp(baseBrickRows, maxBrickRows);

    switch (patternType) {
      case 0:
        _generateCheckerboardPattern(currentRows);
        break;
      case 1:
        _generateDiamondPattern(currentRows);
        break;
      case 2:
        _generateWavePattern(currentRows);
        break;
      case 3:
        _generateRandomPattern(currentRows);
        break;
    }
  }

  void _generateCheckerboardPattern(int rows) {
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < bricksPerRow; col++) {
        if ((row + col) % 2 == 0) {
          _createBrickAt(row, col);
        }
      }
    }
  }

  void _generateDiamondPattern(int rows) {
    final centerRow = rows ~/ 2;
    final centerCol = bricksPerRow ~/ 2;

    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < bricksPerRow; col++) {
        final distanceFromCenter = (row - centerRow).abs() + (col - centerCol).abs();
        if (distanceFromCenter <= centerRow) {
          _createBrickAt(row, col);
        }
      }
    }
  }

  void _generateWavePattern(int rows) {
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < bricksPerRow; col++) {
        final wave = (sin((col / bricksPerRow) * 2 * pi) * 2).round();
        if (row % 3 == (wave + 2) % 3) {
          _createBrickAt(row, col);
        }
      }
    }
  }

  void _generateRandomPattern(int rows) {
    // 70% chance for each position to have a brick
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < bricksPerRow; col++) {
        if (random.nextDouble() < 0.7) {
          _createBrickAt(row, col);
        }
      }
    }
  }

  void _createBrickAt(int row, int col) {
    final x = wallThickness + (col * (brickWidth + 2));
    final y = 50 + (row * (brickHeight + 2));

    final brickType = _determineBrickType(row, col, round);
    final hp = _getBrickHP(brickType, round);

    bricks.add(Brick(
      x: x,
      y: y,
      width: brickWidth,
      height: brickHeight,
      hp: hp,
      maxHp: hp,
      color: _getBrickColor(hp, brickType),
      type: brickType,
      lastRegenTime: brickType == BrickType.regenerating ? DateTime.now() : null,
    ));
  }

  void _gameOver() {
    gameTimer?.cancel();
    
    setState(() {
      isGameOver = true;
      isGameRunning = false;
    });

    // Update high score
    if (score > highScore) {
      highScore = score;
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setBonusGameHighScore('brick_blast', score);
    }

    HapticFeedback.heavyImpact();
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = !isGameRunning;
    });
    
    if (isGameRunning) {
      gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
        _updateGame();
      });
    } else {
      gameTimer?.cancel();
    }
  }

  void _onTapDown(TapDownDetails details) {
    if (!isGameRunning || isGameOver) return;
    
    final localPosition = details.localPosition;
    final center = Offset(gameWidth / 2, gameHeight - 50);
    final direction = (localPosition - center).normalized;
    
    // Launch ball if we have one ready
    final readyBall = balls.where((b) => !b.isLaunched).firstOrNull;
    if (readyBall != null) {
      readyBall.launch(direction * 300); // Launch speed
      setState(() {
        isAiming = false;
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!isGameRunning || isGameOver) return;
    
    final localPosition = details.localPosition;
    final center = Offset(gameWidth / 2, gameHeight - 50);
    
    setState(() {
      isAiming = true;
      aimDirection = (localPosition - center).normalized;
    });
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 12,
            ),
          ),
          Text(
            value.toString(),
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLevelCard() {
    final progressPercent = (round / 1000 * 100).clamp(0, 100);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Level',
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 12,
            ),
          ),
          Text(
            '$round/1000',
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '${progressPercent.toStringAsFixed(1)}%',
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBallStockCard() {
    return GestureDetector(
      onTap: _showBallStockDialog,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: ballStock > 0
              ? NeonColors.surface.withValues(alpha: 0.8)
              : NeonColors.error.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ballStock > 0
                ? NeonColors.primaryAccent.withValues(alpha: 0.3)
                : NeonColors.error.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Ball Stock',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontSize: 12,
              ),
            ),
            Text(
              '$ballStock/$maxBallStock',
              style: GoogleFonts.orbitron(
                color: ballStock > 0 ? NeonColors.primaryAccent : NeonColors.error,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showBallStockDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'Ball Stock Management',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Current Stock: $ballStock/$maxBallStock',
              style: GoogleFonts.orbitron(color: NeonColors.textPrimary),
            ),
            const SizedBox(height: 8),
            Text(
              'Ball HP: ${10 + (ballHpUpgrade * 5)} (Level $ballHpUpgrade)',
              style: GoogleFonts.orbitron(color: NeonColors.textPrimary),
            ),
            const SizedBox(height: 16),
            Text(
              'Balls automatically restock 1 every 30 minutes',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: ballStock < maxBallStock ? _buyBall : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: NeonColors.primaryAccent,
                foregroundColor: NeonColors.background,
              ),
              child: Text(
                'Buy Ball (50 tokens)',
                style: GoogleFonts.orbitron(),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: maxBallStock < 20 ? _upgradeStorage : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: NeonColors.highlights,
                foregroundColor: NeonColors.background,
              ),
              child: Text(
                'Upgrade Storage (200 tokens)',
                style: GoogleFonts.orbitron(),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: ballHpUpgrade < 10 ? _upgradeBallHp : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: NeonColors.error,
                foregroundColor: NeonColors.background,
              ),
              child: Text(
                'Upgrade Ball HP (${100 + (ballHpUpgrade * 50)} tokens)',
                style: GoogleFonts.orbitron(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
            ),
          ),
        ],
      ),
    );
  }

  void _buyBall() async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final success = await gameProvider.spendTokens(
      50,
      TokenTransactionType.powerUpPurchase,
      'BrickBlast ball purchase',
    );

    if (success && mounted) {
      setState(() {
        ballStock = (ballStock + 1).clamp(0, maxBallStock);
      });
      Navigator.of(context).pop();
    }
  }

  void _upgradeStorage() async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final success = await gameProvider.spendTokens(
      200,
      TokenTransactionType.powerUpPurchase,
      'BrickBlast storage upgrade',
    );

    if (success && mounted) {
      setState(() {
        maxBallStock += 5;
      });
      Navigator.of(context).pop();
    }
  }

  void _upgradeBallHp() async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final cost = 100 + (ballHpUpgrade * 50); // Increasing cost: 100, 150, 200, etc.
    final success = await gameProvider.spendTokens(
      cost,
      TokenTransactionType.powerUpPurchase,
      'BrickBlast ball HP upgrade',
    );

    if (success && mounted) {
      setState(() {
        ballHpUpgrade = (ballHpUpgrade + 1).clamp(0, 10);
      });
      _saveGameState(); // Save the upgrade
      Navigator.of(context).pop();
    }
  }

  Widget _buildWalls() {
    return Stack(
      children: [
        // Left wall
        Positioned(
          left: 0,
          top: 0,
          child: Container(
            width: wallThickness,
            height: gameHeight,
            decoration: BoxDecoration(
              color: NeonColors.primaryAccent,
              boxShadow: [
                BoxShadow(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.8),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
        // Right wall
        Positioned(
          right: 0,
          top: 0,
          child: Container(
            width: wallThickness,
            height: gameHeight,
            decoration: BoxDecoration(
              color: NeonColors.primaryAccent,
              boxShadow: [
                BoxShadow(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.8),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
        // Top wall (bounce wall - just above bricks)
        Positioned(
          left: 0,
          top: 40, // Position just above where bricks start (y=50)
          child: Container(
            width: gameWidth,
            height: 10, // Thin wall just above bricks
            decoration: BoxDecoration(
              color: NeonColors.primaryAccent,
              boxShadow: [
                BoxShadow(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.8),
                  blurRadius: 15,
                  spreadRadius: 3,
                ),
              ],
            ),
          ),
        ),
        // Bottom wall
        Positioned(
          left: 0,
          bottom: 0,
          child: Container(
            width: gameWidth,
            height: wallThickness,
            decoration: BoxDecoration(
              color: NeonColors.textSecondary,
              boxShadow: [
                BoxShadow(
                  color: NeonColors.textSecondary.withValues(alpha: 0.6),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildBricks() {
    return bricks.map((brick) {
      final hpRatio = brick.hp / brick.maxHp;
      final alpha = 0.7 + (hpRatio * 0.3); // Keep bricks more visible

      return Positioned(
        left: brick.x,
        top: brick.y,
        child: Container(
          width: brick.width,
          height: brick.height,
          decoration: BoxDecoration(
            color: brick.color.withValues(alpha: alpha),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: brick.color,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: brick.color.withValues(alpha: 0.5),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Center(
            child: Text(
              brick.hp.toString(),
              style: GoogleFonts.orbitron(
                color: NeonColors.background,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  List<Widget> _buildBalls() {
    return balls.map((ball) {
      return Positioned(
        left: ball.position.dx - ball.radius,
        top: ball.position.dy - ball.radius,
        child: Container(
          width: ball.radius * 2,
          height: ball.radius * 2,
          decoration: BoxDecoration(
            color: ball.color.withValues(alpha: ball.glowIntensity),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: ball.color.withValues(alpha: ball.glowIntensity * 0.8),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'BrickBlast - Level $round',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isGameRunning ? Icons.pause : Icons.play_arrow,
              color: NeonColors.primaryAccent,
            ),
            onPressed: isGameOver ? null : _pauseGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonColors.backgroundGradient,
        ),
        child: Column(
          children: [
            // Score display
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildScoreCard('Score', score),
                  _buildScoreCard('High Score', highScore),
                  _buildLevelCard(),
                  _buildBallStockCard(),
                ],
              ),
            ),

            // Game area
            Expanded(
              child: Center(
                child: Container(
                  width: gameWidth,
                  height: gameHeight,
                  decoration: BoxDecoration(
                    color: NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onTapDown: _onTapDown,
                    onPanUpdate: _onPanUpdate,
                    child: Stack(
                      children: [
                        // Game walls
                        _buildWalls(),

                        // Bricks
                        ..._buildBricks(),

                        // Balls
                        ..._buildBalls(),

                        // Aim line
                        if (isAiming && aimDirection != null)
                          _buildAimLine(),

                        // Game over overlay
                        if (isGameOver)
                          _buildGameOverOverlay(),

                        // Start game overlay
                        if (!isGameRunning && !isGameOver)
                          _buildStartGameOverlay(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAimLine() {
    final center = Offset(gameWidth / 2, gameHeight - 50);
    final end = center + (aimDirection! * 100);

    return CustomPaint(
      size: Size(gameWidth, gameHeight),
      painter: AimLinePainter(
        start: center,
        end: end,
        color: NeonColors.primaryAccent.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildGameOverOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Game Over',
              style: GoogleFonts.orbitron(
                color: NeonColors.error,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Final Score: $score',
              style: GoogleFonts.orbitron(
                color: NeonColors.textPrimary,
                fontSize: 20,
              ),
            ),
            Text(
              'Round Reached: $round',
              style: GoogleFonts.orbitron(
                color: NeonColors.textPrimary,
                fontSize: 16,
              ),
            ),
            if (score == highScore) ...[
              const SizedBox(height: 8),
              Text(
                'New High Score!',
                style: GoogleFonts.orbitron(
                  color: NeonColors.success,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _startGame,
              style: ElevatedButton.styleFrom(
                backgroundColor: NeonColors.primaryAccent,
                foregroundColor: NeonColors.background,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text(
                'Play Again',
                style: GoogleFonts.orbitron(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartGameOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'BrickBlast',
              style: GoogleFonts.orbitron(
                color: NeonColors.primaryAccent,
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Aim and launch balls to destroy bricks!\nLeft/Right walls speed up balls\nTop wall destroys balls instantly',
              textAlign: TextAlign.center,
              style: GoogleFonts.orbitron(
                color: NeonColors.textPrimary,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _startGame,
              style: ElevatedButton.styleFrom(
                backgroundColor: NeonColors.primaryAccent,
                foregroundColor: NeonColors.background,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text(
                'Start Game',
                style: GoogleFonts.orbitron(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AimLinePainter extends CustomPainter {
  final Offset start;
  final Offset end;
  final Color color;

  AimLinePainter({
    required this.start,
    required this.end,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    canvas.drawLine(start, end, paint);

    // Draw arrow head
    final direction = (end - start).normalized;
    final arrowLength = 10.0;
    final arrowAngle = 0.5;

    final arrowPoint1 = end - Offset(
      direction.dx * arrowLength * cos(arrowAngle) - direction.dy * arrowLength * sin(arrowAngle),
      direction.dx * arrowLength * sin(arrowAngle) + direction.dy * arrowLength * cos(arrowAngle),
    );

    final arrowPoint2 = end - Offset(
      direction.dx * arrowLength * cos(-arrowAngle) - direction.dy * arrowLength * sin(-arrowAngle),
      direction.dx * arrowLength * sin(-arrowAngle) + direction.dy * arrowLength * cos(-arrowAngle),
    );

    canvas.drawLine(end, arrowPoint1, paint);
    canvas.drawLine(end, arrowPoint2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
