
import '../models/power_up.dart';

/// Game constants and configuration values
class GameConstants {
  // Game settings
  static const String gameTitle = 'TapVerse';
  static const double gameWidth = 400.0;
  static const double gameHeight = 800.0;

  // Level progression - Updated for 100 level system
  static const int maxLevels = 100;
  static const int baseXpReward = 100;
  static const int baseTokenReward = 25;

  // Player progression
  static const int xpPerLevel = 1000;
  static const int startingTokens = 100;

  // Boss levels (level 5 easy, then every 10 levels starting at 10)
  static const List<int> bossLevels = [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

  // Level-specific token rewards (as per documentation)
  static const Map<String, int> levelTokenRewards = {
    'levels_1_4': 25,
    'level_5_boss': 100,
    'levels_6_9': 40,
    'level_10_boss': 100,
    'levels_11_19': 50,
    'level_20_boss': 120,
    'levels_21_49': 60, // Range 60-70
    'level_50_boss': 150,
    'levels_51_79': 130, // Range 130-170
    'level_80_boss': 200,
    'levels_81_99': 180, // Range 180-220
    'level_100_boss': 300,
  };

  // Boss score bonus formula: (Total taps × 10) + (Time remaining × 5)
  static const int bossScoreTapMultiplier = 10;
  static const int bossScoreTimeMultiplier = 5;

  // Power-up costs - Tiered system (Tier 1, 2, 3)
  static const Map<String, Map<int, int>> powerUpCosts = {
    'time_boost': {1: 50, 2: 70, 3: 100},
    'slow_motion': {1: 60, 2: 90, 3: 120},
    'immune': {1: 175, 2: 225, 3: 300},
    'swipe_mode': {1: 250}, // Permanent, single tier
    'multiball': {1: 80, 2: 110, 3: 150},
  };

  // Power-up effects by tier
  static const Map<String, Map<int, Map<String, dynamic>>> powerUpEffects = {
    'time_boost': {
      1: {'timeAdd': 5, 'duration': 0},
      2: {'timeAdd': 8, 'duration': 0},
      3: {'timeAdd': 12, 'duration': 0},
    },
    'slow_motion': {
      1: {'duration': 5, 'slowFactor': 0.5},
      2: {'duration': 7, 'slowFactor': 0.5},
      3: {'duration': 10, 'slowFactor': 0.5},
    },
    'immune': {
      1: {'duration': 5, 'immuneToAll': true},
      2: {'duration': 8, 'immuneToAll': true},
      3: {'duration': 10, 'immuneToAll': true},
    },
    'swipe_mode': {
      1: {'permanent': true, 'swipeEqualsTab': true},
    },
  };

  // Legacy power-up costs for backward compatibility
  static const int slowMotionCost = 60; // Tier 1 cost
  static const int neonBombCost = 75;
  static const int shieldCost = 175; // Renamed to immune, Tier 1 cost
  static const int magnetTouchCost = 60;
  static const int timeBoostCost = 50; // Tier 1 cost

  // Bonus game unlock costs (updated as per documentation)
  static const int neonSnakeCost = 100;
  static const int neonPongCost = 150;
  static const int astroBlasterCost = 200;
  static const int glowFlowCost = 300;

  // Tier upgrade cost multipliers (for calculating upgrade costs)
  static const List<double> tierUpgradeCostMultipliers = [
    1.0,  // Tier 1 (base cost)
    1.5,  // Tier 2 (50% more)
    2.0,  // Tier 3 (100% more)
  ];
  // Note: Crate Smash removed as per documentation

  // Mystery box selection rewards (after each level)
  static const List<int> mysteryBoxRewards = [1, 5, 10, 20];

  // Crate rewards (for future use)
  static const int basicCrateMinTokens = 10;
  static const int basicCrateMaxTokens = 50;
  static const int advancedCrateMinTokens = 50;
  static const int advancedCrateMaxTokens = 150;
  static const int epicCrateMinTokens = 100;
  static const int epicCrateMaxTokens = 300;

  // Target types and scoring (as per documentation) - increased lifetimes for better gameplay
  static const Map<String, Map<String, dynamic>> targetTypes = {
    'easy_ball': {
      'baseScore': 5,
      'maxScore': 10,
      'lifetime': 5.0, // seconds - increased from 3.0
      'movement': 'stationary',
      'penalty': 0,
    },
    'medium_ball': {
      'baseScore': 15,
      'maxScore': 15,
      'lifetime': 7.0, // increased from 5.0
      'movement': 'falling',
      'penalty': 0,
    },
    'hard_ball': {
      'baseScore': 10,
      'splitScore': 20, // 4 x 20 = 80 additional points
      'splitCount': 4,
      'lifetime': 6.0, // increased from 4.0
      'movement': 'stationary',
      'penalty': 0,
    },
    'red_enemy': {
      'baseScore': 0,
      'penalty': -15,
      'lifetime': 6.0, // increased from 4.0
      'movement': 'random',
      'explodes': true,
    },
    'yellow_enemy': {
      'baseScore': 0,
      'penalty': -15, // seconds from timer
      'lifetime': 5.0, // increased from 3.0
      'movement': 'stationary',
      'affectsTimer': true,
    },
  };

  // Background tap penalty
  static const int backgroundTapPenalty = -15;

  // Timer reduction every 5 levels
  static const int timerReductionInterval = 5;
  static const int timerReductionAmount = 5; // seconds
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 600);
  static const Duration longAnimation = Duration(milliseconds: 1000);
  
  // Neon glow settings
  static const double glowRadius = 20.0;
  static const double glowSpread = 5.0;
  static const double pulseScale = 1.2;

  // System UI padding to prevent clipping under OS overlays
  static const double topSystemPadding = 8.0; // Top padding for status bar/notch
  static const double bottomSystemNavPadding = 12.0; // Bottom padding for nav buttons
  static const double sideSystemPadding = 4.0; // Side padding for edge gestures
}

/// Game mode enumeration
enum GameMode {
  tap,
  swipe,
  hold,
  avoid,
  drag,
  memory,
  spin,
  reaction,
  deflect,
  survive
}

/// Crate type enumeration
enum CrateType {
  basic,
  advanced,
  epic
}

/// Power-up type enumeration
enum PowerUpType {
  slowMotion,
  neonBomb,
  shield,
  magnetTouch,
  timeBoost,
  immune, // New: replaces shield in tiered system
  swipeMode, // New: permanent upgrade
  multiball, // New: spawns hard ball that splits into 4 fast balls
}

/// Game state enumeration
enum GameState {
  menu,
  playing,
  paused,
  gameOver,
  levelComplete,
  shop,
  crateOpening
}

/// Difficulty level enumeration
enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert
}

/// Utility functions for game calculations
class GameCalculations {
  /// Calculate token reward for a specific level
  static int getTokenRewardForLevel(int level) {
    if (level >= 1 && level <= 4) return GameConstants.levelTokenRewards['levels_1_4']!;
    if (level == 5) return GameConstants.levelTokenRewards['level_5_boss']!;
    if (level >= 6 && level <= 9) return GameConstants.levelTokenRewards['levels_6_9']!;
    if (level == 10) return GameConstants.levelTokenRewards['level_10_boss']!;
    if (level >= 11 && level <= 19) return GameConstants.levelTokenRewards['levels_11_19']!;
    if (level == 20) return GameConstants.levelTokenRewards['level_20_boss']!;
    if (level >= 21 && level <= 49) {
      // Range 60-70, use level-based variation
      return 60 + ((level - 21) % 11);
    }
    if (level == 50) return GameConstants.levelTokenRewards['level_50_boss']!;
    if (level >= 51 && level <= 79) {
      // Range 130-170, use level-based variation
      return 130 + ((level - 51) % 41);
    }
    if (level == 80) return GameConstants.levelTokenRewards['level_80_boss']!;
    if (level >= 81 && level <= 99) {
      // Range 180-220, use level-based variation
      return 180 + ((level - 81) % 41);
    }
    if (level == 100) return GameConstants.levelTokenRewards['level_100_boss']!;

    return GameConstants.baseTokenReward; // Fallback
  }

  /// Calculate boss score bonus
  static int calculateBossScoreBonus(int totalTaps, int timeRemainingSeconds) {
    return (totalTaps * GameConstants.bossScoreTapMultiplier) +
           (timeRemainingSeconds * GameConstants.bossScoreTimeMultiplier);
  }

  /// Check if level is a boss level
  static bool isBossLevel(int level) {
    return GameConstants.bossLevels.contains(level);
  }

  /// Get power-up cost for specific tier
  static int getPowerUpCost(String powerUpId, int tier) {
    final costs = GameConstants.powerUpCosts[powerUpId];
    if (costs == null || !costs.containsKey(tier)) {
      return 0; // Invalid power-up or tier
    }
    return costs[tier]!;
  }

  /// Get power-up effects for specific tier
  static Map<String, dynamic>? getPowerUpEffects(String powerUpId, int tier) {
    final effects = GameConstants.powerUpEffects[powerUpId];
    if (effects == null || !effects.containsKey(tier)) {
      return null; // Invalid power-up or tier
    }
    return effects[tier]!;
  }

  /// Calculate timer duration for level (reduces every 5 levels)
  static int getTimerForLevel(int level, int baseTimer) {
    final reductions = (level - 1) ~/ GameConstants.timerReductionInterval;
    final reducedTime = baseTimer - (reductions * GameConstants.timerReductionAmount);
    return reducedTime.clamp(10, baseTimer); // Minimum 10 seconds
  }

  /// Calculate power-up upgrade cost for a specific tier
  static int getPowerUpUpgradeCost(String powerUpId, int targetTier) {
    if (targetTier <= 1) return 0;

    // Base cost from tier 1 power-up
    final basePowerUp = PowerUps.createPowerUpAtTier(powerUpId, 1);
    if (basePowerUp == null) return 0;

    // Upgrade cost is base cost * tier multiplier
    return (basePowerUp.cost * GameConstants.tierUpgradeCostMultipliers[targetTier - 1]).round();
  }
}
