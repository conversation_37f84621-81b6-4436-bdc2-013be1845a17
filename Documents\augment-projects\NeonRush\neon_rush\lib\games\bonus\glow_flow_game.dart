import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';

import '../../core/sound_manager.dart';
import '../../models/neon_theme.dart';

import '../../services/challenges_service.dart';
import '../../models/challenge.dart';

/// Glow Flow - Connect flowing neon paths bonus game
class GlowFlowGame extends StatefulWidget {
  final NeonTheme theme;
  final Function(int score, int tokens)? onGameComplete;

  const GlowFlowGame({
    super.key,
    required this.theme,
    this.onGameComplete,
  });

  @override
  State<GlowFlowGame> createState() => _GlowFlowGameState();
}

class _GlowFlowGameState extends State<GlowFlowGame>
    with TickerProviderStateMixin {
  late AnimationController _flowController;
  late AnimationController _pulseController;
  
  // Game state
  bool _isPlaying = false;
  bool _gameOver = false;
  int _score = 0;
  int _level = 1;
  Duration _timeRemaining = const Duration(seconds: 90);
  Timer? _gameTimer;
  
  // Grid system
  static const int _gridSize = 6;
  final List<List<_FlowNode>> _grid = [];
  final List<_FlowPath> _activePaths = [];
  final List<_FlowPath> _completedPaths = [];
  
  // Current drawing
  _FlowPath? _currentPath;
  
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _flowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _flowController.repeat();
    _pulseController.repeat(reverse: true);
    _initializeGrid();
  }

  @override
  void dispose() {
    _flowController.dispose();
    _pulseController.dispose();
    _gameTimer?.cancel();
    super.dispose();
  }

  void _initializeGrid() {
    _grid.clear();
    for (int row = 0; row < _gridSize; row++) {
      _grid.add([]);
      for (int col = 0; col < _gridSize; col++) {
        _grid[row].add(_FlowNode(
          row: row,
          col: col,
          type: _FlowNodeType.empty,
          color: Colors.grey.withValues(alpha: 0.3),
        ));
      }
    }
  }

  void _startGame() {
    setState(() {
      _isPlaying = true;
      _gameOver = false;
      _score = 0;
      _level = 1;
      _timeRemaining = const Duration(seconds: 90);
      _activePaths.clear();
      _completedPaths.clear();
      _currentPath = null;
    });

    _initializeGrid();
    _generateLevel();
    
    // Game timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeRemaining.inSeconds > 0) {
        setState(() {
          _timeRemaining = Duration(seconds: _timeRemaining.inSeconds - 1);
        });
      } else {
        _endGame();
      }
    });
  }

  void _generateLevel() {
    _initializeGrid();
    _activePaths.clear();
    
    // Generate source and target pairs
    final numPairs = 2 + _level;
    final colors = [
      widget.theme.primaryColor,
      widget.theme.accentColor,
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.yellow,
      Colors.purple,
      Colors.orange,
    ];
    
    for (int i = 0; i < numPairs && i < colors.length; i++) {
      final color = colors[i];
      
      // Find random positions for source and target
      late int sourceRow, sourceCol, targetRow, targetCol;
      
      do {
        sourceRow = _random.nextInt(_gridSize);
        sourceCol = _random.nextInt(_gridSize);
      } while (_grid[sourceRow][sourceCol].type != _FlowNodeType.empty);
      
      do {
        targetRow = _random.nextInt(_gridSize);
        targetCol = _random.nextInt(_gridSize);
      } while (_grid[targetRow][targetCol].type != _FlowNodeType.empty ||
               (targetRow == sourceRow && targetCol == sourceCol));
      
      // Set source and target
      _grid[sourceRow][sourceCol] = _FlowNode(
        row: sourceRow,
        col: sourceCol,
        type: _FlowNodeType.source,
        color: color,
        pairId: i,
      );
      
      _grid[targetRow][targetCol] = _FlowNode(
        row: targetRow,
        col: targetCol,
        type: _FlowNodeType.target,
        color: color,
        pairId: i,
      );
    }
  }

  void _endGame() {
    setState(() {
      _isPlaying = false;
      _gameOver = true;
    });
    
    _gameTimer?.cancel();
    
    // Calculate tokens earned
    final tokensEarned = (_score / 50).round().clamp(1, 100);
    
    // Update challenges
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.bonusGames,
      type: ChallengeType.bonusGame,
      amount: 1,
      metadata: {
        'game': 'glow_flow',
        'score': _score,
        'level': _level,
      },
    );
    
    widget.onGameComplete?.call(_score, tokensEarned);
    SoundManager().playGameComplete();
  }

  void _onPanStart(DragStartDetails details, int row, int col) {
    if (!_isPlaying) return;
    
    final node = _grid[row][col];
    if (node.type == _FlowNodeType.source) {
      // Start new path from source
      _currentPath = _FlowPath(
        pairId: node.pairId!,
        color: node.color,
        nodes: [node],
      );
      SoundManager().playButtonClick();
    } else if (node.type == _FlowNodeType.path) {
      // Continue existing path
      final existingPath = _activePaths.firstWhere(
        (path) => path.nodes.any((n) => n.row == row && n.col == col),
        orElse: () => _FlowPath(pairId: -1, color: Colors.transparent, nodes: []),
      );
      
      if (existingPath.pairId != -1) {
        // Remove nodes after this point
        final nodeIndex = existingPath.nodes.indexWhere(
          (n) => n.row == row && n.col == col,
        );
        if (nodeIndex != -1) {
          existingPath.nodes.removeRange(nodeIndex + 1, existingPath.nodes.length);
          _currentPath = existingPath;
        }
      }
    }
  }

  void _onPanUpdate(DragUpdateDetails details, int row, int col) {
    if (!_isPlaying || _currentPath == null) return;
    
    final node = _grid[row][col];
    
    // Check if we can add this node to the path
    if (_canAddNodeToPath(node, _currentPath!)) {
      setState(() {
        if (node.type == _FlowNodeType.empty) {
          // Convert empty node to path node
          _grid[row][col] = _FlowNode(
            row: row,
            col: col,
            type: _FlowNodeType.path,
            color: _currentPath!.color,
            pairId: _currentPath!.pairId,
          );
        }
        
        // Add to current path if not already there
        if (!_currentPath!.nodes.any((n) => n.row == row && n.col == col)) {
          _currentPath!.nodes.add(_grid[row][col]);
        }
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isPlaying || _currentPath == null) return;
    
    // Check if path is complete (ends at target)
    final lastNode = _currentPath!.nodes.last;
    final targetNode = _findTargetForPair(_currentPath!.pairId);
    
    if (targetNode != null && lastNode.row == targetNode.row && lastNode.col == targetNode.col) {
      // Path completed!
      setState(() {
        _completedPaths.add(_currentPath!);
        _activePaths.removeWhere((path) => path.pairId == _currentPath!.pairId);
        _activePaths.add(_currentPath!);
        _score += 100 * _level;
      });
      
      SoundManager().playLevelComplete();
      
      // Check if level is complete
      if (_isLevelComplete()) {
        _nextLevel();
      }
    } else {
      // Add incomplete path to active paths
      if (!_activePaths.any((path) => path.pairId == _currentPath!.pairId)) {
        _activePaths.add(_currentPath!);
      }
    }
    
    _currentPath = null;
  }

  bool _canAddNodeToPath(_FlowNode node, _FlowPath path) {
    if (path.nodes.isEmpty) return false;
    
    final lastNode = path.nodes.last;
    
    // Check if adjacent
    final rowDiff = (node.row - lastNode.row).abs();
    final colDiff = (node.col - lastNode.col).abs();
    if (rowDiff + colDiff != 1) return false;
    
    // Check node type compatibility
    if (node.type == _FlowNodeType.empty) return true;
    if (node.type == _FlowNodeType.target && node.pairId == path.pairId) return true;
    if (node.type == _FlowNodeType.path && node.pairId == path.pairId) return true;
    
    return false;
  }

  _FlowNode? _findTargetForPair(int pairId) {
    for (final row in _grid) {
      for (final node in row) {
        if (node.type == _FlowNodeType.target && node.pairId == pairId) {
          return node;
        }
      }
    }
    return null;
  }

  bool _isLevelComplete() {
    // Check if all source-target pairs are connected
    final sourcePairs = <int>{};
    final completedPairs = <int>{};
    
    for (final row in _grid) {
      for (final node in row) {
        if (node.type == _FlowNodeType.source) {
          sourcePairs.add(node.pairId!);
        }
      }
    }
    
    for (final path in _completedPaths) {
      final lastNode = path.nodes.last;
      final targetNode = _findTargetForPair(path.pairId);
      if (targetNode != null && lastNode.row == targetNode.row && lastNode.col == targetNode.col) {
        completedPairs.add(path.pairId);
      }
    }
    
    return sourcePairs.length == completedPairs.length;
  }

  void _nextLevel() {
    setState(() {
      _level++;
      _timeRemaining = Duration(seconds: _timeRemaining.inSeconds + 30);
    });
    
    _generateLevel();
    SoundManager().playRewardSound();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                colors: [
                  widget.theme.primaryColor.withValues(alpha: 0.1),
                  Colors.black,
                ],
              ),
            ),
          ),
          
          // Game area
          if (_isPlaying)
            Center(
              child: AspectRatio(
                aspectRatio: 1.0,
                child: Container(
                  margin: const EdgeInsets.all(20),
                  child: GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: _gridSize,
                      crossAxisSpacing: 2,
                      mainAxisSpacing: 2,
                    ),
                    itemCount: _gridSize * _gridSize,
                    itemBuilder: (context, index) {
                      final row = index ~/ _gridSize;
                      final col = index % _gridSize;
                      final node = _grid[row][col];
                      
                      return GestureDetector(
                        onPanStart: (details) => _onPanStart(details, row, col),
                        onPanUpdate: (details) => _onPanUpdate(details, row, col),
                        onPanEnd: _onPanEnd,
                        child: AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Container(
                              decoration: BoxDecoration(
                                color: node.color,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: node.type == _FlowNodeType.source || node.type == _FlowNodeType.target
                                      ? Colors.white
                                      : Colors.transparent,
                                  width: 2,
                                ),
                                boxShadow: node.type != _FlowNodeType.empty
                                    ? [
                                        BoxShadow(
                                          color: node.color.withValues(alpha: 0.5 + 0.3 * _pulseController.value),
                                          blurRadius: 10,
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: node.type == _FlowNodeType.source
                                  ? const Icon(Icons.play_arrow, color: Colors.white)
                                  : node.type == _FlowNodeType.target
                                      ? const Icon(Icons.stop, color: Colors.white)
                                      : null,
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          
          // UI
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Score: $_score',
                  style: TextStyle(
                    color: widget.theme.primaryColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Level: $_level',
                  style: TextStyle(
                    color: widget.theme.accentColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Time: ${_timeRemaining.inSeconds}s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Start/Game Over screen
          if (!_isPlaying)
            Container(
              color: Colors.black.withValues(alpha: 0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _gameOver ? 'Game Over!' : 'Glow Flow',
                      style: TextStyle(
                        color: widget.theme.primaryColor,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Connect matching sources and targets',
                      style: TextStyle(
                        color: widget.theme.accentColor,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (_gameOver) ...[
                      const SizedBox(height: 20),
                      Text(
                        'Final Score: $_score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        'Level Reached: $_level',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        ),
                      ),
                    ],
                    const SizedBox(height: 40),
                    ElevatedButton(
                      onPressed: _startGame,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.theme.primaryColor,
                        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                      ),
                      child: Text(
                        _gameOver ? 'Play Again' : 'Start Game',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

enum _FlowNodeType { empty, source, target, path }

class _FlowNode {
  final int row;
  final int col;
  final _FlowNodeType type;
  final Color color;
  final int? pairId;

  _FlowNode({
    required this.row,
    required this.col,
    required this.type,
    required this.color,
    this.pairId,
  });
}

class _FlowPath {
  final int pairId;
  final Color color;
  final List<_FlowNode> nodes;

  _FlowPath({
    required this.pairId,
    required this.color,
    required this.nodes,
  });
}
