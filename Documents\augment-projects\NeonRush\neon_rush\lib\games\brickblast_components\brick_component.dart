import 'dart:math';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';

import '../../models/brickblast_brick.dart';

/// Flame component for rendering a BrickBlast brick
class BrickComponent extends PositionComponent {
  BrickBlastBrick brick;
  late Paint brickPaint;
  late Paint borderPaint;
  late Paint glowPaint;
  late Paint crackPaint;
  
  // Animation properties
  double animationTime = 0.0;
  double shakeOffset = 0.0;
  final Random _random = Random();

  BrickComponent(this.brick) : super(
    position: Vector2(brick.position.dx, brick.position.dy),
    size: Vector2(brick.size.width, brick.size.height),
  ) {
    _updatePaints();
  }

  /// Update paint objects based on brick state
  void _updatePaints() {
    // Main brick paint
    brickPaint = Paint()
      ..color = brick.color
      ..style = PaintingStyle.fill;

    // Border paint
    borderPaint = Paint()
      ..color = brick.borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // Glow effect paint
    glowPaint = Paint()
      ..color = brick.color.withValues(alpha: 0.3 * brick.glowIntensity)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    // Crack paint
    crackPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.7 * brick.crackLevel)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
  }

  /// Update brick data and position
  void updateBrick(BrickBlastBrick newBrick) {
    final oldHp = brick.hp;
    brick = newBrick;
    position = Vector2(brick.position.dx, brick.position.dy);
    _updatePaints();

    // Add Flame effects for damage
    if (newBrick.hp < oldHp && newBrick.hp > 0) {
      addDamageEffect();
    }

    // Add destruction effect if brick is destroyed
    if (newBrick.hp <= 0 && oldHp > 0) {
      addDestructionEffect();
    }

    // Trigger shake animation if brick was damaged (legacy)
    if (brick.shakeIntensity > 0) {
      animationTime = 0.0;
    }
  }

  /// Add visual damage effect using simple animation
  void addDamageEffect() {
    // Simple visual feedback - we'll implement this later with proper Flame effects
    // For now, just update the visual state
    _updatePaints();
  }

  /// Add destruction effect using simple animation
  void addDestructionEffect() {
    // Simple visual feedback - we'll implement this later with proper Flame effects
    // For now, just mark as destroyed
    brick = brick.copyWith(hp: 0);
    _updatePaints();
  }

  @override
  void render(Canvas canvas) {
    if (brick.isDestroyed) return;
    
    // Apply shake offset
    canvas.save();
    if (brick.shakeIntensity > 0) {
      final shakeX = (_random.nextDouble() - 0.5) * brick.shakeIntensity * 4;
      final shakeY = (_random.nextDouble() - 0.5) * brick.shakeIntensity * 4;
      canvas.translate(shakeX, shakeY);
    }
    
    // Draw glow effect
    final glowRect = Rect.fromLTWH(-2, -2, size.x + 4, size.y + 4);
    canvas.drawRRect(
      RRect.fromRectAndRadius(glowRect, const Radius.circular(4)),
      glowPaint,
    );
    
    // Draw main brick
    final brickRect = Rect.fromLTWH(0, 0, size.x, size.y);
    canvas.drawRRect(
      RRect.fromRectAndRadius(brickRect, const Radius.circular(2)),
      brickPaint,
    );
    
    // Draw border
    canvas.drawRRect(
      RRect.fromRectAndRadius(brickRect, const Radius.circular(2)),
      borderPaint,
    );
    
    // Draw HP text
    _renderHPText(canvas);
    
    // Draw cracks
    if (brick.crackLevel > 0) {
      _renderCracks(canvas);
    }
    
    // Draw type-specific effects
    _renderTypeEffects(canvas);
    
    canvas.restore();
  }

  /// Render HP text on the brick
  void _renderHPText(Canvas canvas) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: '${brick.hp}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    final textOffset = Offset(
      (size.x - textPainter.width) / 2,
      (size.y - textPainter.height) / 2,
    );

    textPainter.paint(canvas, textOffset);
  }

  /// Render crack effects based on damage
  void _renderCracks(Canvas canvas) {
    if (brick.crackLevel <= 0) return;
    
    final path = Path();
    final crackIntensity = brick.crackLevel;
    
    // Horizontal crack
    if (crackIntensity > 0.3) {
      path.moveTo(size.x * 0.1, size.y * 0.5);
      path.lineTo(size.x * 0.9, size.y * 0.5);
    }
    
    // Vertical crack
    if (crackIntensity > 0.6) {
      path.moveTo(size.x * 0.5, size.y * 0.1);
      path.lineTo(size.x * 0.5, size.y * 0.9);
    }
    
    // Diagonal cracks
    if (crackIntensity > 0.8) {
      path.moveTo(size.x * 0.2, size.y * 0.2);
      path.lineTo(size.x * 0.8, size.y * 0.8);
      
      path.moveTo(size.x * 0.8, size.y * 0.2);
      path.lineTo(size.x * 0.2, size.y * 0.8);
    }
    
    canvas.drawPath(path, crackPaint);
  }

  /// Render type-specific visual effects
  void _renderTypeEffects(Canvas canvas) {
    switch (brick.type) {
      case BrickType.explosive:
        _renderExplosiveEffect(canvas);
        break;
      case BrickType.shield:
        _renderShieldEffect(canvas);
        break;
      case BrickType.reinforced:
        _renderReinforcedEffect(canvas);
        break;
      default:
        break;
    }
  }

  /// Render explosive brick effect
  void _renderExplosiveEffect(Canvas canvas) {
    final sparkPaint = Paint()
      ..color = Colors.orange.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    // Draw small sparks around the brick
    for (int i = 0; i < 4; i++) {
      final angle = (animationTime * 2 + i * 1.57) % 6.28;
      final sparkX = size.x / 2 + cos(angle) * (size.x / 3);
      final sparkY = size.y / 2 + sin(angle) * (size.y / 3);

      canvas.drawCircle(Offset(sparkX, sparkY), 1.5, sparkPaint);
    }
  }

  /// Render shield brick effect
  void _renderShieldEffect(Canvas canvas) {
    final shieldPaint = Paint()
      ..color = Colors.purple.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Draw shield outline
    final shieldRect = Rect.fromLTWH(-1, -1, size.x + 2, size.y + 2);
    canvas.drawRRect(
      RRect.fromRectAndRadius(shieldRect, const Radius.circular(3)),
      shieldPaint,
    );
  }

  /// Render reinforced brick effect
  void _renderReinforcedEffect(Canvas canvas) {
    final metalPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw metal grid pattern
    const gridSize = 8.0;
    for (double x = gridSize; x < size.x; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.y), metalPaint);
    }
    for (double y = gridSize; y < size.y; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.x, y), metalPaint);
    }
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    animationTime += dt;
    
    // Update position from brick
    position = Vector2(brick.position.dx, brick.position.dy);
    
    // Update paints if brick state changed
    _updatePaints();
    
    // Reduce shake intensity over time
    if (brick.shakeIntensity > 0) {
      brick.shakeIntensity = (brick.shakeIntensity - dt * 2).clamp(0.0, 1.0);
    }
    
    // Add pulsing effect for certain brick types
    if (brick.type == BrickType.explosive) {
      final pulseScale = 1.0 + 0.05 * sin(animationTime * 8);
      scale = Vector2.all(pulseScale);
    } else if (brick.type == BrickType.shield) {
      final pulseScale = 1.0 + 0.03 * sin(animationTime * 4);
      scale = Vector2.all(pulseScale);
    }
    
    // Remove immediately when destroyed
    if (brick.isDestroyed) {
      removeFromParent();
    }
  }
}
