import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/game_provider.dart';
import '../../core/sound_manager.dart';
import '../../models/token_transaction.dart';

/// TapCraft - Tap rapidly to build a neon structure
class Tap<PERSON>raftGame extends StatefulWidget {
  final Function(int score, int tokens)? onGameComplete;

  const TapCraftGame({
    super.key,
    this.onGameComplete,
  });

  @override
  State<TapCraftGame> createState() => _TapCraftGameState();
}

class _TapCraftGameState extends State<TapCraftGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _decayTimer;
  late AnimationController _tapController;
  late Animation<double> _tapAnimation;
  
  final List<CraftBlock> _blocks = [];
  final List<TapEffect> _tapEffects = [];
  double _progress = 0.0;
  double _targetProgress = 100.0;
  bool _gameActive = false;
  int _score = 0;
  int _level = 1;
  int _timeLeft = 30;
  double _decayRate = 0.5; // progress lost per second
  int _tapsThisSecond = 0;
  int _maxTapsPerSecond = 0;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _decayTimer.cancel();
    _tapController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _tapAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _tapController, curve: Curves.easeOut),
    );
  }

  void _startNewGame() {
    setState(() {
      _blocks.clear();
      _tapEffects.clear();
      _progress = 0.0;
      _targetProgress = 100.0;
      _gameActive = true;
      _score = 0;
      _level = 1;
      _timeLeft = 30;
      _decayRate = 0.5;
      _tapsThisSecond = 0;
      _maxTapsPerSecond = 0;
    });

    _startGameTimer();
    _startDecayTimer();
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) return;
      
      setState(() {
        _timeLeft--;
        
        // Track max taps per second
        if (_tapsThisSecond > _maxTapsPerSecond) {
          _maxTapsPerSecond = _tapsThisSecond;
        }
        _tapsThisSecond = 0;
      });
      
      if (_timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _startDecayTimer() {
    _decayTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_gameActive) return;
      
      setState(() {
        _progress = max(0, _progress - (_decayRate * 0.1));
        
        // Remove tap effects older than 1 second
        _tapEffects.removeWhere((effect) {
          return DateTime.now().difference(effect.createdAt).inMilliseconds > 1000;
        });
      });
    });
  }

  void _updateBlocks() {
    final blockHeight = _progress / _targetProgress;
    final blocksNeeded = (blockHeight * 10).floor();
    
    while (_blocks.length < blocksNeeded) {
      final random = Random();
      _blocks.add(CraftBlock(
        x: random.nextDouble() * 200 + 100,
        y: 400 - (_blocks.length * 20),
        width: 80 + random.nextDouble() * 40,
        height: 20,
        color: Color.lerp(
          const Color(0xFF00FFFF),
          const Color(0xFFFF00FF),
          random.nextDouble(),
        )!,
      ));
    }
  }

  void _onTap(TapDownDetails details) async {
    if (!_gameActive) return;
    
    final tapIncrease = 2.0 + (_level * 0.5); // More progress per tap at higher levels
    
    setState(() {
      _progress = min(_targetProgress, _progress + tapIncrease);
      _tapsThisSecond++;
      
      // Add tap effect
      _tapEffects.add(TapEffect(
        x: details.localPosition.dx,
        y: details.localPosition.dy,
        createdAt: DateTime.now(),
      ));
    });
    
    // Update blocks
    _updateBlocks();
    
    // Check for level completion
    if (_progress >= _targetProgress) {
      await _completeLevel();
    } else {
      SoundManager().playSfx(SoundType.buttonClick);
      
      // Tap animation
      _tapController.forward().then((_) {
        _tapController.reverse();
      });
    }
  }

  Future<void> _completeLevel() async {
    setState(() {
      _gameActive = false;
    });

    SoundManager().playSfx(SoundType.buttonClick);

    final timeBonus = _timeLeft * 10;
    final speedBonus = _maxTapsPerSecond * 5;
    setState(() {
      _score += 100 + timeBonus + speedBonus;
      _level++;
    });

    // Start next level or end game
    if (_level <= 3) {
      await Future.delayed(const Duration(seconds: 2));
      setState(() {
        _progress = 0.0;
        _targetProgress += 50; // Increase difficulty
        _timeLeft = 30;
        _decayRate += 0.2; // Faster decay
        _gameActive = true;
        _blocks.clear();
      });
    } else {
      _endGame();
    }
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final tokens = _score ~/ 10; // 1 token per 10 points
    
    await gameProvider.earnTokens(
      tokens,
      TokenTransactionType.bonusGameReward,
      'TapCraft game completed',
    );

    if (widget.onGameComplete != null) {
      widget.onGameComplete!(_score, tokens);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onTapDown: _onTap,
              child: AnimatedBuilder(
                animation: _tapAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _tapAnimation.value,
                    child: SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: CustomPaint(
                        painter: TapCraftPainter(
                          blocks: _blocks,
                          tapEffects: _tapEffects,
                          progress: _progress,
                          targetProgress: _targetProgress,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // UI
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Score: $_score',
                        style: const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      Text(
                        'Level: $_level',
                        style: const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      Text(
                        'Time: $_timeLeft',
                        style: const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Progress bar
                  Container(
                    height: 20,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(9),
                      child: LinearProgressIndicator(
                        value: _progress / _targetProgress,
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CraftBlock {
  final double x;
  final double y;
  final double width;
  final double height;
  final Color color;

  CraftBlock({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
  });
}

class TapEffect {
  final double x;
  final double y;
  final DateTime createdAt;

  TapEffect({
    required this.x,
    required this.y,
    required this.createdAt,
  });
}

/// Custom painter for the tap craft game
class TapCraftPainter extends CustomPainter {
  final List<CraftBlock> blocks;
  final List<TapEffect> tapEffects;
  final double progress;
  final double targetProgress;

  TapCraftPainter({
    required this.blocks,
    required this.tapEffects,
    required this.progress,
    required this.targetProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw base platform
    _drawBase(canvas, size);
    
    // Draw blocks
    for (int i = 0; i < blocks.length; i++) {
      _drawBlock(canvas, size, blocks[i], i);
    }
    
    // Draw tap effects
    for (final effect in tapEffects) {
      _drawTapEffect(canvas, effect);
    }
  }

  void _drawBase(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF333333)
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(50, size.height - 50, size.width - 100, 30);
    canvas.drawRect(rect, paint);
    
    // Base glow
    final glowPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);
    
    canvas.drawRect(rect, glowPaint);
  }

  void _drawBlock(Canvas canvas, Size size, CraftBlock block, int index) {
    final paint = Paint()
      ..color = block.color
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(block.x, block.y, block.width, block.height);
    canvas.drawRect(rect, paint);
    
    // Block glow
    final glowPaint = Paint()
      ..color = block.color.withValues(alpha: 0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    
    canvas.drawRect(rect, glowPaint);
  }

  void _drawTapEffect(Canvas canvas, TapEffect effect) {
    final age = DateTime.now().difference(effect.createdAt).inMilliseconds;
    final opacity = 1.0 - (age / 1000.0);
    
    if (opacity <= 0) return;
    
    final paint = Paint()
      ..color = const Color(0xFFFFFFFF).withValues(alpha: opacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final radius = age / 10.0;
    canvas.drawCircle(Offset(effect.x, effect.y), radius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
