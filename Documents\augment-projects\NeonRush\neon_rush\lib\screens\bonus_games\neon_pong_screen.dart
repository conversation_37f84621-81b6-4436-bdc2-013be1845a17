import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../constants.dart';
import '../../providers/game_provider.dart';

/// Neon Pong bonus game screen
class NeonPongScreen extends StatefulWidget {
  const NeonPongScreen({super.key});

  @override
  State<NeonPongScreen> createState() => _NeonPongScreenState();
}

class _NeonPongScreenState extends State<NeonPongScreen> {
  static const double gameWidth = 400.0;
  static const double gameHeight = 600.0;
  static const double paddleWidth = 80.0;
  static const double paddleHeight = 12.0;
  static const double ballSize = 12.0;
  
  // Game state
  Timer? gameTimer;
  bool isGameRunning = false;
  bool isGameOver = false;
  int playerScore = 0;
  int aiScore = 0;
  int highScore = 0;
  
  // Ball state
  double ballX = gameWidth / 2;
  double ballY = gameHeight / 2;
  double ballVelocityX = 3.0;
  double ballVelocityY = 3.0;
  
  // Paddle positions
  double playerPaddleX = gameWidth / 2 - paddleWidth / 2;
  double aiPaddleX = gameWidth / 2 - paddleWidth / 2;
  
  final Random random = Random();

  @override
  void initState() {
    super.initState();
    _loadHighScore();
    _resetBall();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    super.dispose();
  }

  void _loadHighScore() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    highScore = gameProvider.getBonusGameHighScore('neon_pong');
  }

  void _startGame() {
    setState(() {
      playerScore = 0;
      aiScore = 0;
      isGameRunning = true;
      isGameOver = false;
    });
    
    _resetBall();
    gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      _updateGame();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = !isGameRunning;
    });
    
    if (isGameRunning) {
      gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
        _updateGame();
      });
    } else {
      gameTimer?.cancel();
    }
  }

  void _resetBall() {
    setState(() {
      ballX = gameWidth / 2;
      ballY = gameHeight / 2;
      ballVelocityX = (random.nextBool() ? 1 : -1) * (2.0 + random.nextDouble() * 2.0);
      ballVelocityY = (random.nextBool() ? 1 : -1) * (2.0 + random.nextDouble() * 2.0);
    });
  }

  void _updateGame() {
    if (!isGameRunning || isGameOver) return;

    setState(() {
      // Update ball position
      ballX += ballVelocityX;
      ballY += ballVelocityY;

      // Ball collision with left/right walls
      if (ballX <= 0 || ballX >= gameWidth - ballSize) {
        ballVelocityX = -ballVelocityX;
        ballX = ballX <= 0 ? 0 : gameWidth - ballSize;
      }

      // Ball collision with top wall (AI scores)
      if (ballY <= 0) {
        playerScore++;
        HapticFeedback.lightImpact();
        _resetBall();
        if (playerScore >= 11) {
          _gameOver(true);
          return;
        }
      }

      // Ball collision with bottom wall (Player loses)
      if (ballY >= gameHeight - ballSize) {
        aiScore++;
        HapticFeedback.lightImpact();
        _resetBall();
        if (aiScore >= 11) {
          _gameOver(false);
          return;
        }
      }

      // Ball collision with player paddle
      if (ballY + ballSize >= gameHeight - paddleHeight - 20 &&
          ballY + ballSize <= gameHeight - 20 &&
          ballX + ballSize >= playerPaddleX &&
          ballX <= playerPaddleX + paddleWidth) {
        ballVelocityY = -ballVelocityY.abs();
        // Add some spin based on where the ball hits the paddle
        double hitPosition = (ballX + ballSize / 2 - playerPaddleX) / paddleWidth;
        ballVelocityX += (hitPosition - 0.5) * 2.0;
        HapticFeedback.selectionClick();
      }

      // Ball collision with AI paddle
      if (ballY <= paddleHeight + 20 &&
          ballY >= 20 &&
          ballX + ballSize >= aiPaddleX &&
          ballX <= aiPaddleX + paddleWidth) {
        ballVelocityY = ballVelocityY.abs();
        // Add some spin based on where the ball hits the paddle
        double hitPosition = (ballX + ballSize / 2 - aiPaddleX) / paddleWidth;
        ballVelocityX += (hitPosition - 0.5) * 2.0;
      }

      // AI paddle movement (simple AI)
      double ballCenterX = ballX + ballSize / 2;
      double paddleCenterX = aiPaddleX + paddleWidth / 2;
      double aiSpeed = 2.5;
      
      if (ballCenterX < paddleCenterX - 5) {
        aiPaddleX = (aiPaddleX - aiSpeed).clamp(0, gameWidth - paddleWidth);
      } else if (ballCenterX > paddleCenterX + 5) {
        aiPaddleX = (aiPaddleX + aiSpeed).clamp(0, gameWidth - paddleWidth);
      }

      // Limit ball speed
      ballVelocityX = ballVelocityX.clamp(-8.0, 8.0);
      ballVelocityY = ballVelocityY.clamp(-8.0, 8.0);
    });
  }

  void _gameOver(bool playerWon) {
    gameTimer?.cancel();
    setState(() {
      isGameOver = true;
      isGameRunning = false;
    });

    // Update high score (player score only)
    if (playerScore > highScore) {
      highScore = playerScore;
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setBonusGameHighScore('neon_pong', playerScore);
    }

    HapticFeedback.heavyImpact();
  }

  void _updatePlayerPaddle(double localX) {
    if (!isGameRunning || isGameOver) return;
    
    setState(() {
      playerPaddleX = (localX - paddleWidth / 2).clamp(0, gameWidth - paddleWidth);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Neon Pong',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isGameRunning ? Icons.pause : Icons.play_arrow,
              color: NeonColors.primaryAccent,
            ),
            onPressed: isGameOver ? null : _pauseGame,
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          decoration: const BoxDecoration(
            gradient: NeonColors.backgroundGradient,
          ),
          child: Column(
          children: [
            // Score display
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildScoreCard('You', playerScore),
                  _buildScoreCard('High Score', highScore),
                  _buildScoreCard('AI', aiScore),
                ],
              ),
            ),
            
            // Game area
            Expanded(
              child: Center(
                child: Container(
                  width: gameWidth,
                  height: gameHeight,
                  decoration: BoxDecoration(
                    color: NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onPanUpdate: (details) {
                      _updatePlayerPaddle(details.localPosition.dx);
                    },
                    onTapDown: (details) {
                      _updatePlayerPaddle(details.localPosition.dx);
                    },
                    child: Stack(
                      children: [
                        // Center line
                        Positioned(
                          left: 0,
                          right: 0,
                          top: gameHeight / 2 - 1,
                          child: Container(
                            height: 2,
                            color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                          ),
                        ),
                        
                        // AI paddle
                        Positioned(
                          left: aiPaddleX,
                          top: 20,
                          child: Container(
                            width: paddleWidth,
                            height: paddleHeight,
                            decoration: BoxDecoration(
                              color: NeonColors.error,
                              borderRadius: BorderRadius.circular(6),
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.error.withValues(alpha: 0.5),
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        // Player paddle
                        Positioned(
                          left: playerPaddleX,
                          bottom: 20,
                          child: Container(
                            width: paddleWidth,
                            height: paddleHeight,
                            decoration: BoxDecoration(
                              color: NeonColors.primaryAccent,
                              borderRadius: BorderRadius.circular(6),
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        // Ball
                        Positioned(
                          left: ballX,
                          top: ballY,
                          child: Container(
                            width: ballSize,
                            height: ballSize,
                            decoration: BoxDecoration(
                              color: NeonColors.highlights,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.highlights.withValues(alpha: 0.7),
                                  blurRadius: 12,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        // Game over overlay
                        if (isGameOver)
                          Container(
                            color: Colors.black.withValues(alpha: 0.7),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    playerScore > aiScore ? 'You Win!' : 'AI Wins!',
                                    style: GoogleFonts.orbitron(
                                      color: playerScore > aiScore ? NeonColors.success : NeonColors.error,
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Final Score: $playerScore - $aiScore',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.textPrimary,
                                      fontSize: 18,
                                    ),
                                  ),
                                  if (playerScore == highScore) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      'New High Score!',
                                      style: GoogleFonts.orbitron(
                                        color: NeonColors.success,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 24),
                                  ElevatedButton(
                                    onPressed: _startGame,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: NeonColors.primaryAccent,
                                      foregroundColor: NeonColors.background,
                                    ),
                                    child: Text(
                                      'Play Again',
                                      style: GoogleFonts.orbitron(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Controls info
            if (!isGameOver)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (!isGameRunning && playerScore == 0 && aiScore == 0)
                      ElevatedButton(
                        onPressed: _startGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.primaryAccent,
                          foregroundColor: NeonColors.background,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        ),
                        child: Text(
                          'Start Game',
                          style: GoogleFonts.orbitron(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap or drag to move your paddle',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      'First to 11 points wins!',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 12,
            ),
          ),
          Text(
            value.toString(),
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
