import 'dart:math';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';

/// Background component for BrickBlast with neon grid effect
class BackgroundComponent extends Component with HasGameReference {
  late Paint gridPaint;
  late Paint glowPaint;
  double animationTime = 0.0;
  final Random _random = Random();
  
  // Grid properties
  static const double gridSize = 30.0;
  static const double lineWidth = 1.0;
  
  // Pulsing stars
  final List<PulsingStar> stars = [];

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Initialize paints
    gridPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth;

    glowPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth * 2
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);
    
    // Generate random stars
    _generateStars();
  }

  /// Generate random pulsing stars for background
  void _generateStars() {
    stars.clear();
    
    for (int i = 0; i < 20; i++) {
      stars.add(PulsingStar(
        position: Offset(
          _random.nextDouble() * 400, // Game width
          _random.nextDouble() * 600, // Game height
        ),
        size: _random.nextDouble() * 2 + 1,
        pulseSpeed: _random.nextDouble() * 2 + 1,
        color: _getRandomNeonColor(),
      ));
    }
  }

  /// Get random neon color for stars
  Color _getRandomNeonColor() {
    final colors = [
      const Color(0xFF00FFFF), // Cyan
      const Color(0xFF00FF00), // Green
      const Color(0xFFFF00FF), // Magenta
      const Color(0xFFFFFF00), // Yellow
      const Color(0xFF8000FF), // Purple
    ];
    
    return colors[_random.nextInt(colors.length)];
  }

  @override
  void render(Canvas canvas) {
    final gameSize = game.size;
    
    // Draw gradient background
    _drawGradientBackground(canvas, gameSize);
    
    // Draw grid with glow effect
    _drawGrid(canvas, gameSize);
    
    // Draw pulsing stars
    _drawStars(canvas);
    
    // Draw border glow
    _drawBorderGlow(canvas, gameSize);
  }

  /// Draw gradient background
  void _drawGradientBackground(Canvas canvas, Vector2 gameSize) {
    final rect = Rect.fromLTWH(0, 0, gameSize.x, gameSize.y);
    
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF000020), // Dark blue
        const Color(0xFF000040), // Slightly lighter blue
        const Color(0xFF000020), // Back to dark blue
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRect(rect, paint);
  }

  /// Draw neon grid
  void _drawGrid(Canvas canvas, Vector2 gameSize) {
    // Animate grid opacity
    final pulseOpacity = 0.1 + 0.05 * sin(animationTime * 2);
    
    gridPaint.color = const Color(0xFF00FFFF).withValues(alpha: pulseOpacity);
    glowPaint.color = const Color(0xFF00FFFF).withValues(alpha: pulseOpacity * 0.5);
    
    // Draw vertical lines
    for (double x = 0; x <= gameSize.x; x += gridSize) {
      // Glow effect
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, gameSize.y),
        glowPaint,
      );
      
      // Main line
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, gameSize.y),
        gridPaint,
      );
    }
    
    // Draw horizontal lines
    for (double y = 0; y <= gameSize.y; y += gridSize) {
      // Glow effect
      canvas.drawLine(
        Offset(0, y),
        Offset(gameSize.x, y),
        glowPaint,
      );
      
      // Main line
      canvas.drawLine(
        Offset(0, y),
        Offset(gameSize.x, y),
        gridPaint,
      );
    }
  }

  /// Draw pulsing stars
  void _drawStars(Canvas canvas) {
    for (final star in stars) {
      final pulse = sin(animationTime * star.pulseSpeed) * 0.5 + 0.5;
      final starPaint = Paint()
        ..color = star.color.withValues(alpha: pulse * 0.8)
        ..style = PaintingStyle.fill;

      final glowStarPaint = Paint()
        ..color = star.color.withValues(alpha: pulse * 0.3)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size);
      
      // Draw glow
      canvas.drawCircle(star.position, star.size * 2, glowStarPaint);
      
      // Draw star
      canvas.drawCircle(star.position, star.size, starPaint);
    }
  }

  /// Draw border glow effect
  void _drawBorderGlow(Canvas canvas, Vector2 gameSize) {
    final borderPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);
    
    final borderRect = Rect.fromLTWH(0, 0, gameSize.x, gameSize.y);
    canvas.drawRect(borderRect, borderPaint);
  }

  @override
  void update(double dt) {
    super.update(dt);
    animationTime += dt;
    
    // Occasionally regenerate stars for variety
    if (animationTime % 30 < dt) {
      _generateStars();
    }
  }
}

/// Represents a pulsing star in the background
class PulsingStar {
  final Offset position;
  final double size;
  final double pulseSpeed;
  final Color color;

  PulsingStar({
    required this.position,
    required this.size,
    required this.pulseSpeed,
    required this.color,
  });
}
