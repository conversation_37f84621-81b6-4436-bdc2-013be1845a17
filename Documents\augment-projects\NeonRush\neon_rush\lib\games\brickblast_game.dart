import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/effects.dart';
import 'package:flame/game.dart';
import 'package:flutter/material.dart';

import '../models/brickblast_ball.dart';
import '../models/brickblast_brick.dart';
import '../models/brickblast_game_state.dart';
import '../providers/brickblast_provider.dart';
import 'brickblast_components/ball_component.dart';
import 'brickblast_components/brick_component.dart';
import 'brickblast_components/background_component.dart';
import 'brickblast_components/particle_component.dart';

/// Main Flame game for BrickBlast
class BrickBlastGame extends FlameGame with TapDetector, PanDetector {
  late BrickBlastProvider provider;
  late BackgroundComponent backgroundComponent;
  
  // Game components
  final Map<String, BallComponent> ballComponents = {};
  final Map<String, BrickComponent> brickComponents = {};
  final List<ParticleComponent> particles = [];
  
  // UI components (removed - handled by Flutter overlay)
  
  // Aiming
  Offset? aimStart;
  Offset? aimEnd;
  bool isAiming = false;
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    // Add background
    backgroundComponent = BackgroundComponent();
    add(backgroundComponent);
  }

  /// Initialize with provider
  void initializeWithProvider(BrickBlastProvider gameProvider) {
    provider = gameProvider;
    
    // Set up callbacks
    provider.onParticleEffect = _createParticleEffect;
    provider.onFloatingText = _createFloatingText;
    
    // Initial update
    updateFromGameState();
  }

  /// Update game components from provider state
  void updateFromGameState() {
    final gameState = provider.gameState;
    
    // UI is now handled by Flutter overlay
    
    // Update balls
    _updateBalls(gameState.balls);
    
    // Update bricks
    _updateBricks(gameState.bricks);
  }

  /// Update ball components
  void _updateBalls(List<BrickBlastBall> balls) {
    // Remove old ball components
    for (final component in ballComponents.values) {
      component.removeFromParent();
    }
    ballComponents.clear();
    
    // Add new ball components
    for (final ball in balls) {
      if (ball.isActive) {
        final component = BallComponent(ball);
        ballComponents[ball.id] = component;
        add(component);
      }
    }
  }

  /// Update brick components
  void _updateBricks(List<BrickBlastBrick> bricks) {
    // Remove destroyed brick components
    final toRemove = <String>[];
    for (final entry in brickComponents.entries) {
      final brick = bricks.firstWhere(
        (b) => b.id == entry.key,
        orElse: () => BrickBlastBrick(
          id: '',
          hp: 0,
          maxHp: 0,
          type: BrickType.normal,
          position: Offset.zero,
          isDestroyed: true,
        ),
      );
      
      if (brick.isDestroyed) {
        entry.value.removeFromParent();
        toRemove.add(entry.key);
      } else {
        entry.value.updateBrick(brick);
      }
    }
    
    for (final id in toRemove) {
      brickComponents.remove(id);
    }
    
    // Add new brick components
    for (final brick in bricks) {
      if (!brick.isDestroyed && !brickComponents.containsKey(brick.id)) {
        final component = BrickComponent(brick);
        brickComponents[brick.id] = component;
        add(component);
      }
    }
  }

  /// Create particle effect at position
  void _createParticleEffect(Offset position, Color color) {
    final particle = ParticleComponent(
      position: Vector2(position.dx, position.dy),
      color: color,
    );
    add(particle);
  }

  /// Create floating text at position
  void _createFloatingText(String text, Offset position, Color color) {
    // Create a custom component that supports opacity effects
    final floatingTextComponent = _FloatingTextComponent(
      text: text,
      position: Vector2(position.dx, position.dy),
      color: color,
    );

    add(floatingTextComponent);
  }

  @override
  bool onTapDown(TapDownInfo info) {
    if (provider.gamePhase == BrickBlastGamePhase.aiming) {
      final tapPosition = info.eventPosition.global;
      
      // Calculate direction from ball to tap position
      final balls = provider.balls;
      if (balls.isNotEmpty) {
        final ball = balls.first;
        final ballPosition = Offset(ball.position.dx, ball.position.dy);
        final direction = tapPosition.toOffset() - ballPosition;
        
        // Only allow upward shots
        if (direction.dy < 0) {
          provider.launchBall(direction);
        }
      }
    }
    
    return true;
  }

  @override
  bool onPanStart(DragStartInfo info) {
    if (provider.gamePhase == BrickBlastGamePhase.aiming) {
      aimStart = info.eventPosition.global.toOffset();
      isAiming = true;
    }
    return true;
  }

  @override
  bool onPanUpdate(DragUpdateInfo info) {
    if (isAiming && provider.gamePhase == BrickBlastGamePhase.aiming) {
      aimEnd = info.eventPosition.global.toOffset();
      
      if (aimStart != null && aimEnd != null) {
        final direction = aimEnd! - aimStart!;
        // Only allow upward aiming
        if (direction.dy < 0) {
          provider.setAimDirection(direction);
        }
      }
    }
    return true;
  }

  @override
  bool onPanEnd(DragEndInfo info) {
    if (isAiming && provider.gamePhase == BrickBlastGamePhase.aiming) {
      if (aimStart != null && aimEnd != null) {
        final direction = aimEnd! - aimStart!;
        // Only allow upward shots
        if (direction.dy < 0) {
          provider.launchBall(direction);
        }
      }
    }
    
    isAiming = false;
    aimStart = null;
    aimEnd = null;
    return true;
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw aiming line
    if (isAiming && aimStart != null && aimEnd != null && provider.gamePhase == BrickBlastGamePhase.aiming) {
      final direction = aimEnd! - aimStart!;
      if (direction.dy < 0) {
        final paint = Paint()
          ..color = const Color(0xFF00FFFF).withValues(alpha: 0.7)
          ..strokeWidth = 2.0
          ..style = PaintingStyle.stroke;
        
        // Draw dotted line
        _drawDottedLine(canvas, aimStart!, aimEnd!, paint);
        
        // Draw arrow at end
        _drawArrow(canvas, aimEnd!, direction, paint);
      }
    }
  }

  /// Draw dotted line for aiming
  void _drawDottedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    const dashLength = 5.0;
    const gapLength = 3.0;
    
    final direction = (end - start).normalize();
    final distance = (end - start).distance;
    
    double currentDistance = 0.0;
    bool isDash = true;
    
    while (currentDistance < distance) {
      final segmentLength = isDash ? dashLength : gapLength;
      final segmentEnd = currentDistance + segmentLength;
      
      if (isDash) {
        final segmentStart = start + direction * currentDistance;
        final segmentEndPoint = start + direction * (segmentEnd > distance ? distance : segmentEnd);
        canvas.drawLine(segmentStart, segmentEndPoint, paint);
      }
      
      currentDistance = segmentEnd;
      isDash = !isDash;
    }
  }

  /// Draw arrow for aiming direction
  void _drawArrow(Canvas canvas, Offset position, Offset direction, Paint paint) {
    const arrowSize = 10.0;
    final normalizedDirection = direction.normalize();
    
    final arrowPoint1 = position + Offset(
      -normalizedDirection.dx * arrowSize + normalizedDirection.dy * arrowSize * 0.5,
      -normalizedDirection.dy * arrowSize - normalizedDirection.dx * arrowSize * 0.5,
    );
    
    final arrowPoint2 = position + Offset(
      -normalizedDirection.dx * arrowSize - normalizedDirection.dy * arrowSize * 0.5,
      -normalizedDirection.dy * arrowSize + normalizedDirection.dx * arrowSize * 0.5,
    );
    
    final path = Path()
      ..moveTo(position.dx, position.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..moveTo(position.dx, position.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy);
    
    canvas.drawPath(path, paint);
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Update game state from provider
    if (provider.isGameActive) {
      updateFromGameState();
    }
  }
}

/// Custom floating text component that supports opacity effects
class _FloatingTextComponent extends PositionComponent {
  final String text;
  final Color color;
  late TextComponent _textComponent;
  double _opacity = 1.0;

  _FloatingTextComponent({
    required this.text,
    required Vector2 position,
    required this.color,
  }) : super(position: position);

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    // Create the text component
    _textComponent = TextComponent(
      text: text,
      textRenderer: TextPaint(
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );

    add(_textComponent);

    // Add floating animation
    add(
      MoveEffect.by(
        Vector2(0, -50),
        EffectController(duration: 1.0),
        onComplete: () => removeFromParent(),
      ),
    );

    // Add custom fade out effect using a timer
    add(
      TimerComponent(
        period: 0.016, // ~60 FPS
        repeat: true,
        onTick: () {
          _opacity -= 0.016; // Fade out over 1 second
          if (_opacity <= 0) {
            _opacity = 0;
            removeFromParent();
          } else {
            // Update text color with new opacity
            _textComponent.textRenderer = TextPaint(
              style: TextStyle(
                color: color.withValues(alpha: _opacity),
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            );
          }
        },
      ),
    );
  }
}

/// Extension to normalize Offset
extension OffsetExtension on Offset {
  Offset normalize() {
    final length = distance;
    if (length == 0) return Offset.zero;
    return this / length;
  }
}
