import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'neon_effects.dart';
import 'neon_text.dart';

/// A button widget with neon glow effects
class NeonButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color glowColor;
  final Color? backgroundColor;
  final Color? textColor;
  final double width;
  final double height;
  final double borderRadius;
  final double fontSize;
  final FontWeight fontWeight;
  final bool animate;
  final bool enabled;
  final Widget? icon;
  final EdgeInsetsGeometry? padding;
  final double glowIntensity;

  const NeonButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.glowColor,
    this.backgroundColor,
    this.textColor,
    this.width = 200.0,
    this.height = 60.0,
    this.borderRadius = 12.0,
    this.fontSize = 18.0,
    this.fontWeight = FontWeight.w600,
    this.animate = true,
    this.enabled = true,
    this.icon,
    this.padding,
    this.glowIntensity = 1.0,
  });

  /// Creates a large primary button
  const NeonButton.primary({
    super.key,
    required this.text,
    required this.onPressed,
    required this.glowColor,
    this.backgroundColor,
    this.textColor,
    this.animate = true,
    this.enabled = true,
    this.icon,
    this.padding,
    this.glowIntensity = 1.2,
  })  : width = 250.0,
        height = 70.0,
        borderRadius = 15.0,
        fontSize = 20.0,
        fontWeight = FontWeight.w700;

  /// Creates a medium secondary button
  const NeonButton.secondary({
    super.key,
    required this.text,
    required this.onPressed,
    required this.glowColor,
    this.backgroundColor,
    this.textColor,
    this.animate = false,
    this.enabled = true,
    this.icon,
    this.padding,
    this.glowIntensity = 0.8,
  })  : width = 180.0,
        height = 50.0,
        borderRadius = 10.0,
        fontSize = 16.0,
        fontWeight = FontWeight.w500;

  /// Creates a small action button
  const NeonButton.small({
    super.key,
    required this.text,
    required this.onPressed,
    required this.glowColor,
    this.backgroundColor,
    this.textColor,
    this.animate = false,
    this.enabled = true,
    this.icon,
    this.padding,
    this.glowIntensity = 0.6,
  })  : width = 120.0,
        height = 40.0,
        borderRadius = 8.0,
        fontSize = 14.0,
        fontWeight = FontWeight.w500;

  @override
  State<NeonButton> createState() => _NeonButtonState();
}

class _NeonButtonState extends State<NeonButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enabled && widget.onPressed != null) {
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enabled && widget.onPressed != null) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
      widget.onPressed!();
    }
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        widget.glowColor.withValues(alpha: 0.1);
    final effectiveTextColor = widget.textColor ?? widget.glowColor;
    final effectiveGlowIntensity = widget.enabled 
        ? widget.glowIntensity 
        : widget.glowIntensity * 0.3;

    Widget buttonContent = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding ?? const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: widget.glowColor.withValues(alpha: widget.enabled ? 0.8 : 0.3),
          width: 2.0,
        ),
        boxShadow: widget.enabled
            ? NeonEffects.createGlow(
                color: widget.glowColor,
                intensity: _isPressed 
                    ? effectiveGlowIntensity * 1.5 
                    : effectiveGlowIntensity,
              )
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.icon != null) ...[
            widget.icon!,
            const SizedBox(width: 8.0),
          ],
          Flexible(
            child: NeonText.button(
              widget.text,
              glowColor: effectiveTextColor,
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight,
              glowIntensity: effectiveGlowIntensity,
            ),
          ),
        ],
      ),
    );

    if (widget.animate && widget.enabled) {
      buttonContent = buttonContent.withPulse();
    }

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: buttonContent,
          );
        },
      ),
    );
  }
}

/// A circular button with neon glow effects
class NeonCircularButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color glowColor;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double iconSize;
  final bool animate;
  final bool enabled;
  final double glowIntensity;

  const NeonCircularButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.glowColor,
    this.backgroundColor,
    this.iconColor,
    this.size = 60.0,
    this.iconSize = 24.0,
    this.animate = true,
    this.enabled = true,
    this.glowIntensity = 1.0,
  });

  @override
  State<NeonCircularButton> createState() => _NeonCircularButtonState();
}

class _NeonCircularButtonState extends State<NeonCircularButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enabled && widget.onPressed != null) {
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enabled && widget.onPressed != null) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
      widget.onPressed!();
    }
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        widget.glowColor.withValues(alpha: 0.1);
    final effectiveIconColor = widget.iconColor ?? widget.glowColor;
    final effectiveGlowIntensity = widget.enabled 
        ? widget.glowIntensity 
        : widget.glowIntensity * 0.3;

    Widget buttonContent = Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: widget.glowColor.withValues(alpha: widget.enabled ? 0.8 : 0.3),
          width: 2.0,
        ),
        boxShadow: widget.enabled
            ? NeonEffects.createGlow(
                color: widget.glowColor,
                intensity: _isPressed 
                    ? effectiveGlowIntensity * 1.5 
                    : effectiveGlowIntensity,
              )
            : null,
      ),
      child: Icon(
        widget.icon,
        size: widget.iconSize,
        color: effectiveIconColor,
      ),
    );

    if (widget.animate && widget.enabled) {
      buttonContent = buttonContent.withPulse();
    }

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: buttonContent,
          );
        },
      ),
    );
  }
}

/// A toggle button with neon glow effects
class NeonToggleButton extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Color glowColor;
  final Color? inactiveColor;
  final String? activeText;
  final String? inactiveText;
  final IconData? activeIcon;
  final IconData? inactiveIcon;
  final double width;
  final double height;
  final bool animate;

  const NeonToggleButton({
    super.key,
    required this.value,
    required this.onChanged,
    required this.glowColor,
    this.inactiveColor,
    this.activeText,
    this.inactiveText,
    this.activeIcon,
    this.inactiveIcon,
    this.width = 120.0,
    this.height = 50.0,
    this.animate = true,
  });

  @override
  State<NeonToggleButton> createState() => _NeonToggleButtonState();
}

class _NeonToggleButtonState extends State<NeonToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    if (widget.value) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(NeonToggleButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      if (widget.value) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inactiveColor = widget.inactiveColor ?? Colors.grey;
    
    return GestureDetector(
      onTap: () {
        widget.onChanged(!widget.value);
        HapticFeedback.lightImpact();
      },
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          final currentColor = Color.lerp(
            inactiveColor,
            widget.glowColor,
            _animation.value,
          )!;
          
          final currentText = widget.value 
              ? (widget.activeText ?? 'ON')
              : (widget.inactiveText ?? 'OFF');
              
          final currentIcon = widget.value 
              ? widget.activeIcon
              : widget.inactiveIcon;

          Widget buttonContent = Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: currentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(widget.height / 2),
              border: Border.all(
                color: currentColor,
                width: 2.0,
              ),
              boxShadow: NeonEffects.createGlow(
                color: currentColor,
                intensity: _animation.value,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (currentIcon != null) ...[
                  Icon(
                    currentIcon,
                    color: currentColor,
                    size: 20.0,
                  ),
                  const SizedBox(width: 8.0),
                ],
                NeonText.button(
                  currentText,
                  glowColor: currentColor,
                  fontSize: 14.0,
                  glowIntensity: _animation.value,
                ),
              ],
            ),
          );

          if (widget.animate && widget.value) {
            buttonContent = buttonContent.withPulse();
          }

          return buttonContent;
        },
      ),
    );
  }
}
