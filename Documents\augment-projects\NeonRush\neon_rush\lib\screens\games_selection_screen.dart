import 'package:flutter/material.dart';

import '../core/sound_manager.dart';
import '../core/constants.dart';
import '../models/neon_theme.dart';
import '../ui/neon_widgets.dart';
import 'levels_screen.dart';

/// Games selection screen showing different game types
class GamesSelectionScreen extends StatefulWidget {
  const GamesSelectionScreen({super.key});

  @override
  State<GamesSelectionScreen> createState() => _GamesSelectionScreenState();
}

class _GamesSelectionScreenState extends State<GamesSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _backgroundController.repeat();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.black,
                  NeonThemes.cyberBlue.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  NeonThemes.neonPink.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  NeonThemes.electricGreen.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  Colors.black,
                ],
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildGamesGrid(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Back button
          NeonCircularButton(
            icon: Icons.arrow_back,
            glowColor: NeonThemes.cyberBlue.primary,
            onPressed: () async {
              await SoundManager().playSfx(SoundType.buttonClick);
              if (mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          
          const SizedBox(width: 20),
          
          // Title
          Expanded(
            child: NeonText.title(
              'SELECT GAME',
              glowColor: NeonThemes.cyberBlue.primary,
              fontSize: 32,
              fontWeight: FontWeight.w900,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGamesGrid() {
    final games = [
      GameInfo(
        id: 'tap_target',
        name: 'Tap Target',
        description: 'Tap the glowing targets as fast as you can!',
        color: NeonThemes.electricGreen.primary,
        icon: Icons.touch_app,
        gameMode: GameMode.tap,
      ),
      GameInfo(
        id: 'swipe_avoid',
        name: 'Swipe to Avoid',
        description: 'Swipe to dodge incoming obstacles!',
        color: NeonThemes.neonPink.primary,
        icon: Icons.swipe,
        gameMode: GameMode.swipe,
      ),
      GameInfo(
        id: 'hold_glow',
        name: 'Hold the Glow',
        description: 'Hold to keep the glow alive!',
        color: NeonThemes.cyberBlue.primary,
        icon: Icons.radio_button_checked,
        gameMode: GameMode.hold,
      ),
      GameInfo(
        id: 'avoid_spikes',
        name: 'Avoid Neon Spikes',
        description: 'Navigate through dangerous neon spikes!',
        color: NeonThemes.neonPink.primary,
        icon: Icons.warning,
        gameMode: GameMode.avoid,
      ),
    ];

    return Padding(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
        ),
        itemCount: games.length,
        itemBuilder: (context, index) {
          final game = games[index];
          return _buildGameCard(game);
        },
      ),
    );
  }

  Widget _buildGameCard(GameInfo game) {
    return GestureDetector(
      onTap: () => _navigateToLevels(game),
      child: NeonContainer.card(
        glowColor: game.color,
        glowIntensity: 1.0,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Game icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: game.color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                  boxShadow: NeonEffects.createGlow(
                    color: game.color,
                    intensity: 0.8,
                  ),
                ),
                child: Icon(
                  game.icon,
                  color: game.color,
                  size: 40,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Game name
              NeonText.body(
                game.name,
                glowColor: game.color,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Game description
              NeonText.body(
                game.description,
                glowColor: Colors.grey,
                fontSize: 12,
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Play button
              NeonText.body(
                'TAP TO PLAY',
                glowColor: game.color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToLevels(GameInfo game) async {
    await SoundManager().playSfx(SoundType.buttonClick);

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const LevelsScreen(),
        ),
      );
    }
  }
}

/// Game information class
class GameInfo {
  final String id;
  final String name;
  final String description;
  final Color color;
  final IconData icon;
  final GameMode gameMode;

  const GameInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.color,
    required this.icon,
    required this.gameMode,
  });
}
