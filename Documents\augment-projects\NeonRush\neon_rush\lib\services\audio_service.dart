import 'package:audioplayers/audioplayers.dart';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing game audio including background music and sound effects
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  // Audio players
  late AudioPlayer _musicPlayer;
  late AudioPlayer _sfxPlayer;

  // Settings
  bool _musicEnabled = true;
  bool _sfxEnabled = true;
  double _musicVolume = 0.7;
  double _sfxVolume = 0.8;

  // Current state
  String? _currentMusicTrack;
  bool _isInitialized = false;

  // Storage keys
  static const String _musicEnabledKey = 'music_enabled';
  static const String _sfxEnabledKey = 'sfx_enabled';
  static const String _musicVolumeKey = 'music_volume';
  static const String _sfxVolumeKey = 'sfx_volume';

  // Audio file paths
  static const String menuTheme = 'sounds/Menu Theme.wav';
  static const String gameTheme1 = 'sounds/Game theme 1.wav';
  static const String gameTheme2 = 'sounds/Game theme 2.wav';
  static const String gameTheme3 = 'sounds/Game theme 3.mp3';
  
  // Sound effects
  static const String buttonClick = 'sounds/button click.wav';
  static const String reward = 'sounds/reward.wav';
  static const String addTime = 'sounds/Add time.wav';
  static const String shield = 'sounds/Shield.wav';
  static const String neonBomb = 'sounds/neon bomb.wav';
  static const String slowMo = 'sounds/slow mo.wav';

  /// Initialize the audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _musicPlayer = AudioPlayer();
    _sfxPlayer = AudioPlayer();

    // Load settings
    await _loadSettings();

    // Configure players
    await _musicPlayer.setReleaseMode(ReleaseMode.loop);
    await _sfxPlayer.setReleaseMode(ReleaseMode.stop);

    // Set initial volumes
    await _musicPlayer.setVolume(_musicVolume);
    await _sfxPlayer.setVolume(_sfxVolume);

    _isInitialized = true;
  }

  /// Load audio settings from storage
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    _musicEnabled = prefs.getBool(_musicEnabledKey) ?? true;
    _sfxEnabled = prefs.getBool(_sfxEnabledKey) ?? true;
    _musicVolume = prefs.getDouble(_musicVolumeKey) ?? 0.7;
    _sfxVolume = prefs.getDouble(_sfxVolumeKey) ?? 0.8;
  }

  /// Save audio settings to storage
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(_musicEnabledKey, _musicEnabled);
    await prefs.setBool(_sfxEnabledKey, _sfxEnabled);
    await prefs.setDouble(_musicVolumeKey, _musicVolume);
    await prefs.setDouble(_sfxVolumeKey, _sfxVolume);
  }

  /// Play background music
  Future<void> playBackgroundMusic(String trackPath) async {
    if (!_isInitialized) await initialize();
    
    if (!_musicEnabled) return;
    
    // Don't restart if already playing the same track
    if (_currentMusicTrack == trackPath && _musicPlayer.state == PlayerState.playing) {
      return;
    }

    try {
      await _musicPlayer.stop();
      await _musicPlayer.play(AssetSource(trackPath));
      _currentMusicTrack = trackPath;
    } catch (e) {
      developer.log('Error playing background music: $e', name: 'AudioService');
    }
  }

  /// Stop background music
  Future<void> stopBackgroundMusic() async {
    if (!_isInitialized) return;
    
    try {
      await _musicPlayer.stop();
      _currentMusicTrack = null;
    } catch (e) {
      developer.log('Error stopping background music: $e', name: 'AudioService');
    }
  }

  /// Pause background music
  Future<void> pauseBackgroundMusic() async {
    if (!_isInitialized) return;
    
    try {
      await _musicPlayer.pause();
    } catch (e) {
      developer.log('Error pausing background music: $e', name: 'AudioService');
    }
  }

  /// Resume background music
  Future<void> resumeBackgroundMusic() async {
    if (!_isInitialized) return;
    
    if (!_musicEnabled) return;
    
    try {
      await _musicPlayer.resume();
    } catch (e) {
      developer.log('Error resuming background music: $e', name: 'AudioService');
    }
  }

  /// Play sound effect
  Future<void> playSoundEffect(String soundPath) async {
    if (!_isInitialized) await initialize();
    
    if (!_sfxEnabled) return;

    try {
      await _sfxPlayer.play(AssetSource(soundPath));
    } catch (e) {
      developer.log('Error playing sound effect: $e', name: 'AudioService');
    }
  }

  /// Set music enabled/disabled
  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    await _saveSettings();
    
    if (!enabled) {
      await stopBackgroundMusic();
    } else if (_currentMusicTrack != null) {
      await playBackgroundMusic(_currentMusicTrack!);
    }
  }

  /// Set sound effects enabled/disabled
  Future<void> setSfxEnabled(bool enabled) async {
    _sfxEnabled = enabled;
    await _saveSettings();
  }

  /// Set music volume (0.0 to 1.0)
  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
    
    if (_isInitialized) {
      await _musicPlayer.setVolume(_musicVolume);
    }
  }

  /// Set sound effects volume (0.0 to 1.0)
  Future<void> setSfxVolume(double volume) async {
    _sfxVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
    
    if (_isInitialized) {
      await _sfxPlayer.setVolume(_sfxVolume);
    }
  }

  // Getters
  bool get musicEnabled => _musicEnabled;
  bool get sfxEnabled => _sfxEnabled;
  double get musicVolume => _musicVolume;
  double get sfxVolume => _sfxVolume;
  String? get currentMusicTrack => _currentMusicTrack;
  bool get isInitialized => _isInitialized;

  /// Dispose audio players
  Future<void> dispose() async {
    if (_isInitialized) {
      await _musicPlayer.dispose();
      await _sfxPlayer.dispose();
      _isInitialized = false;
    }
  }

  /// Convenience methods for specific tracks
  Future<void> playMenuMusic() async {
    await playBackgroundMusic(menuTheme);
  }

  Future<void> playGameMusic() async {
    await playBackgroundMusic(gameTheme1);
  }

  /// Convenience methods for sound effects
  Future<void> playButtonClick() async {
    await playSoundEffect(buttonClick);
  }

  Future<void> playReward() async {
    await playSoundEffect(reward);
  }

  Future<void> playAddTime() async {
    await playSoundEffect(addTime);
  }

  Future<void> playShield() async {
    await playSoundEffect(shield);
  }

  Future<void> playNeonBomb() async {
    await playSoundEffect(neonBomb);
  }

  Future<void> playSlowMo() async {
    await playSoundEffect(slowMo);
  }
}
